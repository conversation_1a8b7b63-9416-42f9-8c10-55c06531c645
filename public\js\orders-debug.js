// This file has been replaced by orders.js for canonical Orders JS asset. Please use orders.js.
// Order Management Debug Script
console.log('🔧 Order Management Debug Script Loaded');

// Check if required dependencies are available
function checkDependencies() {
  console.log('🔍 Checking dependencies...');
  
  const dependencies = {
    'jQuery': typeof $ !== 'undefined',
    'Bootstrap': typeof bootstrap !== 'undefined',
    'Chart.js': typeof Chart !== 'undefined',
    'Font Awesome': document.querySelector('link[href*="font-awesome"]') !== null
  };
  
  console.table(dependencies);
  
  // Check for missing dependencies
  const missing = Object.entries(dependencies)
    .filter(([name, available]) => !available)
    .map(([name]) => name);
    
  if (missing.length > 0) {
    console.warn('❌ Missing dependencies:', missing);
    return false;
  } else {
    console.log('✅ All dependencies available');
    return true;
  }
}

// Check if DOM elements exist
function checkDOMElements() {
  console.log('🔍 Checking DOM elements...');
  
  const elements = {
    'Dashboard Container': document.querySelector('.dashboard-container'),
    'Customer Chart': document.getElementById('customerChart'),
    'Shipping Chart': document.getElementById('shippingChart'),
    'Sales Chart': document.getElementById('salesChart'),
    'Orders Table': document.getElementById('ordersTableBody'),
    'Search Input': document.getElementById('searchInput'),
    'Status Filter': document.getElementById('statusFilter'),
    'Refresh Button': document.getElementById('refreshBtn'),
    'Export Button': document.getElementById('exportBtn'),
    'Shipping Modal': document.getElementById('shippingModal'),
    'Promotion Modal': document.getElementById('promotionModal'),
    'Tracking Modal': document.getElementById('trackingModal')
  };
  
  const results = {};
  Object.entries(elements).forEach(([name, element]) => {
    results[name] = element !== null;
    if (!element) {
      console.warn(`❌ Missing element: ${name}`);
    }
  });
  
  console.table(results);
  return results;
}

// Test basic functionality
function testBasicFunctionality() {
  console.log('🔍 Testing basic functionality...');
  
  try {
    // Test notification system
    if (typeof showNotification === 'function') {
      console.log('✅ showNotification function available');
      showNotification('Debug test notification', 'info');
    } else {
      console.warn('❌ showNotification function not available');
    }
    
    // Test chart creation
    if (typeof Chart !== 'undefined') {
      const testCanvas = document.createElement('canvas');
      testCanvas.width = 100;
      testCanvas.height = 100;
      document.body.appendChild(testCanvas);
      
      try {
        const testChart = new Chart(testCanvas, {
          type: 'doughnut',
          data: {
            labels: ['Test'],
            datasets: [{
              data: [100],
              backgroundColor: ['#007bff']
            }]
          },
          options: {
            responsive: false,
            maintainAspectRatio: false
          }
        });
        console.log('✅ Chart.js working correctly');
        testChart.destroy();
        document.body.removeChild(testCanvas);
      } catch (error) {
        console.error('❌ Chart.js error:', error);
        document.body.removeChild(testCanvas);
      }
    }
    
    // Test Bootstrap modals
    if (typeof bootstrap !== 'undefined') {
      console.log('✅ Bootstrap available');
      
      // Test if modals can be created
      const testModal = document.createElement('div');
      testModal.className = 'modal';
      testModal.innerHTML = '<div class="modal-dialog"><div class="modal-content">Test</div></div>';
      document.body.appendChild(testModal);
      
      try {
        const modal = new bootstrap.Modal(testModal);
        console.log('✅ Bootstrap Modal working');
        document.body.removeChild(testModal);
      } catch (error) {
        console.error('❌ Bootstrap Modal error:', error);
        document.body.removeChild(testModal);
      }
    }
    
  } catch (error) {
    console.error('❌ Error in basic functionality test:', error);
  }
}

// Run diagnostics when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 Running Order Management Diagnostics...');
  
  setTimeout(() => {
    checkDependencies();
    checkDOMElements();
    testBasicFunctionality();
    
    console.log('🏁 Diagnostics complete. Check the results above.');
  }, 1000);
});

// Export functions for manual testing
window.orderDebug = {
  checkDependencies,
  checkDOMElements,
  testBasicFunctionality
};
