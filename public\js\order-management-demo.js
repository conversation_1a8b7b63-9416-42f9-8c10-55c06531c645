// public/js/order-management-demo.js
// Canonical JS for Order Management Demo page. Add page-specific logic here.

document.addEventListener('DOMContentLoaded', function () {
  // Dropdown menu for status filter (if present)
  const statusDropdown = document.getElementById('orderStatusDropdown');
  if (statusDropdown) {
    statusDropdown.addEventListener('change', function () {
      const status = statusDropdown.value;
      const container = document.getElementById('ordersContainer');
      container.innerHTML = `<p>Loading ${status ? status : 'all'} orders...</p>`;
      let filter = { limit: 20 };
      if (status) filter.status = status;
      orderManager.getOrders(filter)
        .then(result => {
          window.displayOrders(result.data, container);
        })
        .catch(error => {
          container.innerHTML = `<p class="alert alert-danger">Error loading orders: ${error.message}</p>`;
        });
    });
  }

  // Update Status Form Handler
  const updateStatusForm = document.getElementById('updateStatusForm');
  if (updateStatusForm) {
    updateStatusForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const form = e.target;
      const formData = new FormData(form);
      const orderId = formData.get('orderId');
      const status = formData.get('status');
      try {
        form.classList.add('loading');
        const updatedOrder = await window.orderManager.updateOrderStatus(orderId, status);
        window.orderManager.showSuccess(`Order #${orderId} status updated to ${status}!`);
        form.reset();
        // Refresh orders if they're currently displayed
        const ordersContainer = document.getElementById('ordersContainer');
        if (ordersContainer && ordersContainer.querySelector('.orders-table')) {
          if (typeof window.loadAllOrders === 'function') {
            window.loadAllOrders();
          }
        }
      } catch (error) {
        window.orderManager.showError(error.message);
      } finally {
        form.classList.remove('loading');
      }
    });
  }

  // Quick Status Update
  window.quickStatusUpdate = async function(orderId) {
    const newStatus = prompt('Enter new status (pending, processing, shipped, delivered, completed, cancelled):');
    if (!newStatus) return;
    try {
      await window.orderManager.updateOrderStatus(orderId, newStatus);
      window.orderManager.showSuccess(`Order #${orderId} status updated!`);
      if (typeof window.loadAllOrders === 'function') {
        window.loadAllOrders();
      }
    } catch (error) {
      window.orderManager.showError(error.message);
    }
  };
});

console.log('Order Management Demo JS loaded.');
