<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MockAPI Order Function Tester</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f9fa;
        }
        .result-box {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #198754; }
        .error { color: #dc3545; }
        .info { color: #0d6efd; }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-vial me-2"></i>MockAPI Order Function Tester
                </h1>
                <p class="text-muted">Test your order management functions with realistic data</p>
            </div>
        </div>

        <!-- Test Section 1: Create Order -->
        <div class="test-section">
            <h3><i class="fas fa-plus-circle me-2 text-success"></i>Create Test Order</h3>
            <p>Create a realistic order with random customer data and products</p>
            
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Customer Name</label>
                    <input type="text" class="form-control" id="customerName" value="Jessica Martinez">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Customer Email</label>
                    <input type="email" class="form-control" id="customerEmail" value="<EMAIL>">
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <label class="form-label">Phone</label>
                    <input type="text" class="form-control" id="customerPhone" value="(*************">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Total Price</label>
                    <input type="number" class="form-control" id="totalPrice" value="67.48" step="0.01">
                </div>
            </div>
            
            <div class="mt-3">
                <label class="form-label">Address</label>
                <input type="text" class="form-control" id="customerAddress" value="1247 Sunset Boulevard, Los Angeles, CA 90026">
            </div>
            
            <div class="mt-3">
                <button class="btn btn-success" onclick="createTestOrder()">
                    <i class="fas fa-plus me-1"></i>Create Order
                </button>
                <button class="btn btn-outline-success ms-2" onclick="generateRandomOrder()">
                    <i class="fas fa-random me-1"></i>Generate Random
                </button>
            </div>
            
            <div id="createResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Test Section 2: Fetch Orders -->
        <div class="test-section">
            <h3><i class="fas fa-download me-2 text-primary"></i>Fetch Orders</h3>
            <p>Test fetching orders from MockAPI and local API</p>
            
            <button class="btn btn-primary" onclick="fetchMockAPIOrders()">
                <i class="fas fa-cloud me-1"></i>Fetch from MockAPI
            </button>
            <button class="btn btn-outline-primary ms-2" onclick="fetchLocalOrders()">
                <i class="fas fa-server me-1"></i>Fetch from Local API
            </button>
            
            <div id="fetchResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Test Section 3: Update Order -->
        <div class="test-section">
            <h3><i class="fas fa-edit me-2 text-warning"></i>Update Order</h3>
            <p>Test updating an existing order status and details</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">Order ID</label>
                    <input type="text" class="form-control" id="updateOrderId" placeholder="Enter order ID">
                </div>
                <div class="col-md-4">
                    <label class="form-label">New Status</label>
                    <select class="form-select" id="newStatus">
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Tracking Number</label>
                    <input type="text" class="form-control" id="trackingNumber" placeholder="Optional">
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-warning" onclick="updateOrder()">
                    <i class="fas fa-save me-1"></i>Update Order
                </button>
                <button class="btn btn-outline-warning ms-2" onclick="getLastOrderId()">
                    <i class="fas fa-search me-1"></i>Get Last Order ID
                </button>
            </div>
            
            <div id="updateResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Test Section 4: Delete Order -->
        <div class="test-section">
            <h3><i class="fas fa-trash me-2 text-danger"></i>Delete Order</h3>
            <p>Test deleting an order from MockAPI</p>
            
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Order ID to Delete</label>
                    <input type="text" class="form-control" id="deleteOrderId" placeholder="Enter order ID">
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-danger" onclick="deleteOrder()">
                    <i class="fas fa-trash me-1"></i>Delete Order
                </button>
            </div>
            
            <div id="deleteResult" class="result-box" style="display: none;"></div>
        </div>

        <!-- Test Section 5: Bulk Operations -->
        <div class="test-section">
            <h3><i class="fas fa-layer-group me-2 text-info"></i>Bulk Operations</h3>
            <p>Test creating multiple orders and bulk operations</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">Number of Orders</label>
                    <input type="number" class="form-control" id="bulkCount" value="5" min="1" max="20">
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-info" onclick="createBulkOrders()">
                    <i class="fas fa-plus-square me-1"></i>Create Bulk Orders
                </button>
                <button class="btn btn-outline-info ms-2" onclick="clearAllTestOrders()">
                    <i class="fas fa-broom me-1"></i>Clear Test Orders
                </button>
            </div>
            
            <div id="bulkResult" class="result-box" style="display: none;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const MOCKAPI_URL = 'https://681a3a3c1ac1155635084e35.mockapi.io/orders';
        const LOCAL_API_URL = '/api/orders';
        
        let lastCreatedOrderId = null;

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `<div class="${type}">${content}</div>`;
        }

        function setLoading(elementId, isLoading) {
            const element = document.getElementById(elementId);
            if (isLoading) {
                element.classList.add('loading');
            } else {
                element.classList.remove('loading');
            }
        }

        async function createTestOrder() {
            const resultId = 'createResult';
            setLoading(resultId, true);
            
            try {
                const orderData = {
                    customerName: document.getElementById('customerName').value,
                    customerEmail: document.getElementById('customerEmail').value,
                    customerPhone: document.getElementById('customerPhone').value,
                    customerAddress: document.getElementById('customerAddress').value,
                    totalPrice: parseFloat(document.getElementById('totalPrice').value),
                    customerId: `CUST_${Date.now()}`,
                    items: [
                        {
                            productId: "TSH_COTTON_BLK_M",
                            name: "Premium Cotton T-Shirt - Black - Medium",
                            price: 24.99,
                            quantity: 2
                        },
                        {
                            productId: "MUG_CERAMIC_WHT_15OZ",
                            name: "Ceramic Coffee Mug - White - 15oz",
                            price: 12.50,
                            quantity: 1
                        }
                    ],
                    status: "pending",
                    paymentMethod: "Credit Card",
                    shippingMethod: "Standard Ground",
                    createdAt: new Date().toISOString()
                };

                const response = await fetch(MOCKAPI_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                lastCreatedOrderId = result.id;
                
                showResult(resultId, `
                    <strong>✅ Order Created Successfully!</strong><br>
                    Order ID: ${result.id}<br>
                    Customer: ${result.customerName}<br>
                    Total: $${result.totalPrice}<br>
                    Status: ${result.status}<br>
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result, null, 2)}
                `, 'success');

            } catch (error) {
                showResult(resultId, `❌ Error creating order: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        function generateRandomOrder() {
            const customers = [
                { name: "Jessica Martinez", email: "<EMAIL>", phone: "(*************" },
                { name: "Michael Chen", email: "<EMAIL>", phone: "(*************" },
                { name: "Amanda Thompson", email: "<EMAIL>", phone: "(*************" },
                { name: "Robert Davis", email: "<EMAIL>", phone: "(*************" }
            ];
            
            const customer = customers[Math.floor(Math.random() * customers.length)];
            const price = (Math.random() * 100 + 20).toFixed(2);
            
            document.getElementById('customerName').value = customer.name;
            document.getElementById('customerEmail').value = customer.email;
            document.getElementById('customerPhone').value = customer.phone;
            document.getElementById('totalPrice').value = price;
        }

        async function fetchMockAPIOrders() {
            const resultId = 'fetchResult';
            setLoading(resultId, true);
            
            try {
                const response = await fetch(MOCKAPI_URL);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const orders = await response.json();
                
                showResult(resultId, `
                    <strong>✅ Fetched ${orders.length} orders from MockAPI</strong><br>
                    <br>
                    <strong>Recent Orders:</strong><br>
                    ${orders.slice(-5).map(order => 
                        `ID: ${order.id} | ${order.customerName} | $${order.totalPrice} | ${order.status}`
                    ).join('<br>')}
                    <br><br>
                    <strong>Full Response (last 3 orders):</strong><br>
                    ${JSON.stringify(orders.slice(-3), null, 2)}
                `, 'success');

            } catch (error) {
                showResult(resultId, `❌ Error fetching orders: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        async function fetchLocalOrders() {
            const resultId = 'fetchResult';
            setLoading(resultId, true);
            
            try {
                const response = await fetch(LOCAL_API_URL);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                const orders = result.data || result;
                
                showResult(resultId, `
                    <strong>✅ Fetched orders from Local API</strong><br>
                    Count: ${Array.isArray(orders) ? orders.length : 'Unknown'}<br>
                    <br>
                    <strong>Response:</strong><br>
                    ${JSON.stringify(result, null, 2)}
                `, 'success');

            } catch (error) {
                showResult(resultId, `❌ Error fetching local orders: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        async function updateOrder() {
            const resultId = 'updateResult';
            const orderId = document.getElementById('updateOrderId').value;
            
            if (!orderId) {
                showResult(resultId, '❌ Please enter an Order ID', 'error');
                return;
            }
            
            setLoading(resultId, true);
            
            try {
                const updateData = {
                    status: document.getElementById('newStatus').value,
                    updatedAt: new Date().toISOString()
                };
                
                const trackingNumber = document.getElementById('trackingNumber').value;
                if (trackingNumber) {
                    updateData.trackingNumber = trackingNumber;
                }

                const response = await fetch(`${MOCKAPI_URL}/${orderId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                showResult(resultId, `
                    <strong>✅ Order Updated Successfully!</strong><br>
                    Order ID: ${result.id}<br>
                    New Status: ${result.status}<br>
                    ${result.trackingNumber ? `Tracking: ${result.trackingNumber}<br>` : ''}
                    <br>
                    <strong>Full Response:</strong><br>
                    ${JSON.stringify(result, null, 2)}
                `, 'success');

            } catch (error) {
                showResult(resultId, `❌ Error updating order: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        function getLastOrderId() {
            if (lastCreatedOrderId) {
                document.getElementById('updateOrderId').value = lastCreatedOrderId;
                document.getElementById('deleteOrderId').value = lastCreatedOrderId;
                showResult('updateResult', `ℹ️ Using last created order ID: ${lastCreatedOrderId}`, 'info');
            } else {
                showResult('updateResult', '⚠️ No order has been created yet in this session', 'error');
            }
        }

        async function deleteOrder() {
            const resultId = 'deleteResult';
            const orderId = document.getElementById('deleteOrderId').value;
            
            if (!orderId) {
                showResult(resultId, '❌ Please enter an Order ID', 'error');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete order ${orderId}?`)) {
                return;
            }
            
            setLoading(resultId, true);
            
            try {
                const response = await fetch(`${MOCKAPI_URL}/${orderId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                showResult(resultId, `
                    <strong>✅ Order Deleted Successfully!</strong><br>
                    Deleted Order ID: ${orderId}
                `, 'success');

            } catch (error) {
                showResult(resultId, `❌ Error deleting order: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        async function createBulkOrders() {
            const resultId = 'bulkResult';
            const count = parseInt(document.getElementById('bulkCount').value);
            
            setLoading(resultId, true);
            showResult(resultId, `🔄 Creating ${count} orders...`, 'info');
            
            const customers = [
                { name: "Jessica Martinez", email: "<EMAIL>" },
                { name: "Michael Chen", email: "<EMAIL>" },
                { name: "Amanda Thompson", email: "<EMAIL>" },
                { name: "Robert Davis", email: "<EMAIL>" },
                { name: "Emily Rodriguez", email: "<EMAIL>" }
            ];
            
            const results = [];
            
            for (let i = 0; i < count; i++) {
                try {
                    const customer = customers[i % customers.length];
                    const orderData = {
                        customerName: customer.name,
                        customerEmail: customer.email,
                        customerPhone: `(555) ${Math.floor(Math.random() * 900 + 100)}-${Math.floor(Math.random() * 9000 + 1000)}`,
                        totalPrice: Math.round((Math.random() * 100 + 20) * 100) / 100,
                        customerId: `BULK_${Date.now()}_${i}`,
                        items: [{
                            productId: "TSH_COTTON_BLK_M",
                            name: "Test Product",
                            price: 24.99,
                            quantity: 1
                        }],
                        status: ["pending", "processing", "shipped"][Math.floor(Math.random() * 3)],
                        createdAt: new Date().toISOString()
                    };

                    const response = await fetch(MOCKAPI_URL, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(orderData)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        results.push(result);
                    }
                    
                    // Small delay to avoid rate limiting
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    console.error(`Error creating order ${i + 1}:`, error);
                }
            }
            
            showResult(resultId, `
                <strong>✅ Created ${results.length}/${count} orders successfully!</strong><br>
                <br>
                <strong>Created Orders:</strong><br>
                ${results.map(order => 
                    `ID: ${order.id} | ${order.customerName} | $${order.totalPrice}`
                ).join('<br>')}
            `, 'success');
            
            setLoading(resultId, false);
        }

        async function clearAllTestOrders() {
            if (!confirm('This will delete ALL orders from MockAPI. Are you sure?')) {
                return;
            }
            
            const resultId = 'bulkResult';
            setLoading(resultId, true);
            showResult(resultId, '🔄 Fetching and deleting all orders...', 'info');
            
            try {
                // First, get all orders
                const response = await fetch(MOCKAPI_URL);
                const orders = await response.json();
                
                let deleted = 0;
                for (const order of orders) {
                    try {
                        const deleteResponse = await fetch(`${MOCKAPI_URL}/${order.id}`, {
                            method: 'DELETE'
                        });
                        if (deleteResponse.ok) deleted++;
                        await new Promise(resolve => setTimeout(resolve, 100));
                    } catch (error) {
                        console.error(`Error deleting order ${order.id}:`, error);
                    }
                }
                
                showResult(resultId, `
                    <strong>✅ Cleanup Complete!</strong><br>
                    Deleted ${deleted}/${orders.length} orders
                `, 'success');
                
            } catch (error) {
                showResult(resultId, `❌ Error during cleanup: ${error.message}`, 'error');
            } finally {
                setLoading(resultId, false);
            }
        }

        // Initialize with random data
        generateRandomOrder();
    </script>
</body>
</html>
