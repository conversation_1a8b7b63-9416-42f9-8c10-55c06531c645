import express from 'express';
import { loadAccounting, saveAccounting } from '../../utils/accountingLoader.js';

const router = express.Router();

// Get all accounting entries
router.get('/', async (req, res) => {
    try {
        const accounting = await loadAccounting();
        res.json(accounting);
    } catch (error) {
        console.error('Error fetching accounting data:', error);
        res.status(500).json({ error: 'Failed to fetch accounting data' });
    }
});

// Add a new accounting entry
router.post('/', async (req, res) => {
    try {
        const newEntry = req.body;
        const accounting = await loadAccounting();
        // Defensive: ensure numeric ID and date
        newEntry.id = accounting.length > 0 ? Math.max(...accounting.map(a => Number(a.id) || 0)) + 1 : 1;
        if (!newEntry.date) newEntry.date = new Date().toISOString();
        accounting.push(newEntry);
        await saveAccounting(accounting);
        res.status(201).json(newEntry);
    } catch (error) {
        console.error('Error adding accounting entry:', error);
        res.status(500).json({ error: 'Failed to add accounting entry' });
    }
});

// (Optional) Get a single accounting entry by ID
router.get('/:id', async (req, res) => {
    try {
        const accounting = await loadAccounting();
        const entry = accounting.find(a => String(a.id) === req.params.id);
        if (!entry) return res.status(404).json({ error: 'Entry not found' });
        res.json(entry);
    } catch (error) {
        console.error('Error fetching accounting entry:', error);
        res.status(500).json({ error: 'Failed to fetch accounting entry' });
    }
});

// (Optional) Delete an accounting entry by ID
router.delete('/:id', async (req, res) => {
    try {
        let accounting = await loadAccounting();
        const initialLength = accounting.length;
        accounting = accounting.filter(a => String(a.id) !== req.params.id);
        if (accounting.length === initialLength) {
            return res.status(404).json({ error: 'Entry not found' });
        }
        await saveAccounting(accounting);
        res.json({ success: true });
    } catch (error) {
        console.error('Error deleting accounting entry:', error);
        res.status(500).json({ error: 'Failed to delete accounting entry' });
    }
});

export default router;