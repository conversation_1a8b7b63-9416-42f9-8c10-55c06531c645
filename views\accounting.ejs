<main class="content-area">
    <!-- Financial Intelligence Dashboard -->
    <div class="floating-card mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>Financial Intelligence Dashboard
                </h2>
                <p class="text-muted mb-0">Monitor costs, track profits, and analyze financial performance
                </p>
            </div>
            <div class="d-flex gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="exportDropdown"
                        data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="#" data-action="export-pdf"><i
                                    class="fas fa-file-pdf me-2"></i>Export as PDF</a></li>
                        <li><a class="dropdown-item" href="#" data-action="export-csv"><i
                                    class="fas fa-file-csv me-2"></i>Export as CSV</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" data-action="refresh-data"><i
                                    class="fas fa-sync me-2"></i>Refresh Data</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="floating-card h-100">
                <div class="card-body text-center">
                    <div class="financial-icon revenue mb-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="financial-value mb-1" id="totalRevenue">$0.00</h3>
                    <p class="text-muted mb-0">Total Revenue</p>
                    <small class="text-success" id="revenueChange">+0.0%</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="floating-card h-100">
                <div class="card-body text-center">
                    <div class="financial-icon costs mb-3">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h3 class="financial-value mb-1" id="totalCosts">$0.00</h3>
                    <p class="text-muted mb-0">Total Costs</p>
                    <small class="text-info" id="costsBreakdown">Materials + Shipping</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="floating-card h-100">
                <div class="card-body text-center">
                    <div class="financial-icon profit mb-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="financial-value mb-1" id="totalProfit">$0.00</h3>
                    <p class="text-muted mb-0">Total Profit</p>
                    <small class="profit-indicator" id="profitChange">+0.0%</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="floating-card h-100">
                <div class="card-body text-center">
                    <div class="financial-icon margin mb-3">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h3 class="financial-value mb-1" id="avgProfitMargin">0.0%</h3>
                    <p class="text-muted mb-0">Avg Profit Margin</p>
                    <small class="text-muted" id="marginRange">Range: 0% - 0%</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="floating-card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Advanced Filters
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-lg-3 col-md-6">
                    <label for="searchInput" class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchInput"
                        placeholder="Customer, email, phone, order #...">
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="statusFilter" class="form-label">Status</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="dateFromInput" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="dateFromInput">
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="dateToInput" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="dateToInput">
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="minCostInput" class="form-label">Min Cost</label>
                    <input type="number" class="form-control" id="minCostInput" placeholder="0.00" step="0.01">
                </div>
                <div class="col-lg-1 col-md-6 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-lg-2 col-md-6">
                    <label for="maxCostInput" class="form-label">Max Cost</label>
                    <input type="number" class="form-control" id="maxCostInput" placeholder="1000.00" step="0.01">
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="minProfitInput" class="form-label">Min Profit %</label>
                    <input type="number" class="form-control" id="minProfitInput" placeholder="0" step="0.1">
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="maxProfitInput" class="form-label">Max Profit %</label>
                    <input type="number" class="form-control" id="maxProfitInput" placeholder="100" step="0.1">
                </div>
                <div class="col-lg-6 col-md-12 d-flex align-items-end">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Showing <span id="filteredCount">0</span> of <span id="totalCount">0</span> orders
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Data Table -->
    <div class="floating-card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>Order Financial Analysis
                </h5>
                <div class="d-flex gap-2">
                    <div class="badge bg-primary-soft text-primary px-3 py-2">
                        <i class="fas fa-calculator me-1"></i>
                        <span id="tableOrderCount">0</span> Orders
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="accountingTable">
                <thead class="table-light">
                    <tr>
                        <th style="width: 100px;">Order ID</th>
                        <th style="width: 180px;">Customer</th>
                        <th style="width: 120px;" class="d-none d-md-table-cell">Order Date</th>
                        <th style="width: 100px;">Order Total</th>
                        <th style="width: 110px;" class="d-none d-lg-table-cell">Material Cost</th>
                        <th style="width: 110px;" class="d-none d-lg-table-cell">Shipping Cost</th>
                        <th style="width: 100px;">Total Cost</th>
                        <th style="width: 100px;">Profit</th>
                        <th style="width: 100px;">Margin %</th>
                        <th style="width: 100px;" class="d-none d-md-table-cell">Status</th>
                        <th style="width: 80px;" class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody id="accountingTableBody">
                    <tr>
                        <td colspan="11" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Loading financial data...</h5>
                                <p class="text-muted mb-0">Please wait while we calculate costs and profits</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 mb-0">Calculating financial data...</p>
        </div>
    </div>

    <!-- Order Detail Modal -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1" aria-labelledby="orderDetailModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="orderDetailModalLabel">Order Financial Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="orderDetailContent">
                    <!-- Order details will be populated here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</main>
