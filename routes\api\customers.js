import express from 'express';
import { loadCustomers } from '../../utils/customersLoader.js';
import customerController from '../../controllers/customerController.js';
import { body, validationResult } from 'express-validator';

const router = express.Router();

// Get all customers
router.get('/', async (req, res) => {
    try {
        const customers = await loadCustomers();
        res.json(customers);
    } catch (error) {
        console.error('Error fetching customers:', error.stack);
        res.status(500).json({ error: 'Failed to fetch customers. Please try again later.' });
    }
});

// POST add customer
router.post(
  '/',
  [
    body('name').isString().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('address').isString().isLength({ min: 5 }).withMessage('Address must be at least 5 characters'),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      await customerController.addCustomer(req, res);
    } catch (error) {
      console.error('Error adding customer:', error.stack);
      res.status(400).json({ error: error.message });
    }
  }
);

// GET customer by ID
router.get('/:id', async (req, res) => {
    try {
        await customerController.getCustomer(req, res);
    } catch (error) {
        console.error('Error fetching customer:', error.stack);
        res.status(500).json({ error: 'Failed to fetch customer. Please try again later.' });
    }
});

// PUT update customer
router.put(
  '/:id',
  [
    body('name').optional().isString().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('email').optional().isEmail().withMessage('Valid email is required'),
    body('address').optional().isString().isLength({ min: 5 }).withMessage('Address must be at least 5 characters'),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      await customerController.updateCustomer(req, res);
    } catch (error) {
      console.error('Error updating customer:', error.stack);
      res.status(400).json({ error: error.message });
    }
  }
);

// DELETE customer
router.delete('/:id', async (req, res) => {
    try {
        await customerController.deleteCustomer(req, res);
    } catch (error) {
        console.error('Error deleting customer:', error.stack);
        res.status(500).json({ error: 'Failed to delete customer. Please try again later.' });
    }
});

// GET customer profile with order statistics
router.get('/profile', async (req, res) => {
    try {
        const { email } = req.query;

        if (!email) {
            return res.status(400).json({ error: 'Email parameter is required' });
        }

        // Load customers and orders data
        const customers = await loadCustomers();
        const { loadOrders } = await import('../../utils/ordersLoader.js');
        const orders = await loadOrders();

        // Find customer by email
        const customer = customers.find(c => c.email.toLowerCase() === email.toLowerCase());

        if (!customer) {
            return res.status(404).json({ error: 'Customer not found' });
        }

        // Get customer's orders
        const customerOrders = orders.filter(order =>
            order.customerEmail && order.customerEmail.toLowerCase() === email.toLowerCase()
        );

        // Calculate statistics
        const totalOrders = customerOrders.length;
        const totalSpent = customerOrders.reduce((sum, order) => sum + parseFloat(order.totalPrice || 0), 0);
        const averageOrder = totalOrders > 0 ? totalSpent / totalOrders : 0;
        const customerSince = customerOrders.length > 0 ?
            customerOrders.reduce((earliest, order) => {
                const orderDate = new Date(order.createdAt);
                return orderDate < earliest ? orderDate : earliest;
            }, new Date()).toISOString() : customer.createdAt;

        // Get recent orders (last 5)
        const recentOrders = customerOrders
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        res.json({
            customer,
            totalOrders,
            totalSpent,
            averageOrder,
            customerSince,
            recentOrders
        });

    } catch (error) {
        console.error('Error fetching customer profile:', error.stack);
        res.status(500).json({ error: 'Failed to fetch customer profile. Please try again later.' });
    }
});

export default router;