#!/usr/bin/env node

/**
 * Quick Order Creation Utility for MockAPI Testing
 * Creates realistic orders for testing your functions
 */

import fetch from 'node-fetch';

const MOCKAPI_URL = 'https://681a3a3c1ac1155635084e35.mockapi.io/orders';

// Real-world order template
function createRealisticOrder(customerData = {}) {
  const defaultCustomer = {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    address: "123 Main Street, Anytown, ST 12345"
  };

  const customer = { ...defaultCustomer, ...customerData };
  
  // Realistic product catalog
  const products = [
    { id: "TSH_COTTON_BLK_M", name: "Premium Cotton T-Shirt - Black - Medium", price: 24.99, category: "Apparel" },
    { id: "TSH_COTTON_WHT_L", name: "Premium Cotton T-Shirt - White - Large", price: 24.99, category: "Apparel" },
    { id: "HOD_FLEECE_GRY_XL", name: "<PERSON><PERSON><PERSON>", price: 45.00, category: "Apparel" },
    { id: "MUG_CERAMIC_BLU_15OZ", name: "Ceramic Coffee Mug - Blue - 15oz", price: 12.50, category: "Drinkware" },
    { id: "MUG_CERAMIC_WHT_11OZ", name: "Ceramic Coffee Mug - White - 11oz", price: 10.99, category: "Drinkware" },
    { id: "BAG_TOTE_CAN_LRG", name: "Canvas Tote Bag - Large", price: 18.99, category: "Bags" },
    { id: "STK_VINYL_LOGO_3IN", name: "Logo Vinyl Sticker - 3 inch", price: 3.99, category: "Accessories" },
    { id: "PIN_ENAMEL_LOGO", name: "Enamel Logo Pin", price: 8.50, category: "Accessories" },
    { id: "CAP_BASEBALL_BLK", name: "Baseball Cap - Black", price: 22.00, category: "Apparel" },
    { id: "BTL_WATER_STEEL_32OZ", name: "Stainless Steel Water Bottle - 32oz", price: 28.99, category: "Drinkware" }
  ];

  // Randomly select 1-4 items
  const numItems = Math.floor(Math.random() * 4) + 1;
  const selectedProducts = [];
  const usedProducts = new Set();

  for (let i = 0; i < numItems; i++) {
    let product;
    do {
      product = products[Math.floor(Math.random() * products.length)];
    } while (usedProducts.has(product.id));
    
    usedProducts.add(product.id);
    
    const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
    selectedProducts.push({
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: quantity,
      sku: product.id.replace(/_/g, '-'),
      category: product.category
    });
  }

  // Calculate totals
  const subtotal = selectedProducts.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const taxRate = 0.08; // 8% tax
  const tax = subtotal * taxRate;
  const shippingCost = subtotal > 50 ? 0 : 5.99; // Free shipping over $50
  const totalPrice = subtotal + tax + shippingCost;

  // Random status
  const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
  const status = statuses[Math.floor(Math.random() * statuses.length)];

  // Random payment method
  const paymentMethods = ['Credit Card', 'PayPal', 'Apple Pay', 'Google Pay'];
  const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

  // Random shipping method
  const shippingMethods = ['Standard Ground', 'Express', 'Overnight', 'Economy'];
  const shippingMethod = shippingMethods[Math.floor(Math.random() * shippingMethods.length)];

  return {
    customerName: customer.name,
    customerEmail: customer.email,
    customerPhone: customer.phone,
    customerAddress: customer.address,
    customerId: `CUST_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
    items: selectedProducts,
    totalPrice: Math.round(totalPrice * 100) / 100,
    subtotal: Math.round(subtotal * 100) / 100,
    tax: Math.round(tax * 100) / 100,
    shipping: shippingCost,
    status: status,
    paymentMethod: paymentMethod,
    paymentStatus: status === 'cancelled' ? 'refunded' : 'completed',
    shippingMethod: shippingMethod,
    shippingCarrier: shippingMethod.includes('Express') || shippingMethod.includes('Overnight') ? 'FedEx' : 'UPS',
    trackingNumber: status === 'shipped' || status === 'delivered' ? `1Z${Math.random().toString(36).substr(2, 16).toUpperCase()}` : null,
    orderSource: Math.random() > 0.5 ? 'website' : 'mobile_app',
    notes: Math.random() > 0.7 ? 'Customer requested gift wrapping' : null,
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(), // Random date within last 30 days
    estimatedDelivery: status === 'pending' || status === 'processing' ? 
      new Date(Date.now() + Math.floor(Math.random() * 7 + 3) * 24 * 60 * 60 * 1000).toISOString() : null
  };
}

// Predefined realistic customers
const realisticCustomers = [
  { name: "Jessica Martinez", email: "<EMAIL>", phone: "(*************", address: "1247 Sunset Blvd, Los Angeles, CA 90026" },
  { name: "Michael Chen", email: "<EMAIL>", phone: "(*************", address: "456 Market St, San Francisco, CA 94102" },
  { name: "Amanda Thompson", email: "<EMAIL>", phone: "(*************", address: "789 Pine Ridge Dr, Denver, CO 80203" },
  { name: "Robert Davis", email: "<EMAIL>", phone: "(*************", address: "321 Broadway Ave, New York, NY 10001" },
  { name: "Emily Rodriguez", email: "<EMAIL>", phone: "(*************", address: "654 Oak Tree Ln, Houston, TX 77001" },
  { name: "David Kim", email: "<EMAIL>", phone: "(*************", address: "987 Cedar St, Seattle, WA 98101" },
  { name: "Lisa Wang", email: "<EMAIL>", phone: "(*************", address: "147 Elm St, Boston, MA 02101" },
  { name: "Christopher Brown", email: "<EMAIL>", phone: "(*************", address: "258 Maple Ave, Atlanta, GA 30301" }
];

async function createTestOrder(customerIndex = null) {
  try {
    console.log('🛒 Creating realistic test order...');
    
    // Use specific customer or random one
    const customer = customerIndex !== null ? 
      realisticCustomers[customerIndex] : 
      realisticCustomers[Math.floor(Math.random() * realisticCustomers.length)];
    
    const order = createRealisticOrder(customer);
    
    console.log(`📦 Order Details:`);
    console.log(`   Customer: ${order.customerName} (${order.customerEmail})`);
    console.log(`   Items: ${order.items.length} products`);
    console.log(`   Total: $${order.totalPrice}`);
    console.log(`   Status: ${order.status}`);
    
    // Send to MockAPI
    const response = await fetch(MOCKAPI_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(order)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const createdOrder = await response.json();
    console.log(`✅ Order created successfully with ID: ${createdOrder.id}`);
    console.log(`🔗 View at: ${MOCKAPI_URL}/${createdOrder.id}`);
    
    return createdOrder;
    
  } catch (error) {
    console.error(`❌ Failed to create order: ${error.message}`);
    throw error;
  }
}

async function createMultipleOrders(count = 5) {
  console.log(`🚀 Creating ${count} realistic test orders...`);
  
  const createdOrders = [];
  
  for (let i = 0; i < count; i++) {
    try {
      console.log(`\n📋 Creating order ${i + 1}/${count}...`);
      const order = await createTestOrder();
      createdOrders.push(order);
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 300));
      
    } catch (error) {
      console.error(`❌ Failed to create order ${i + 1}: ${error.message}`);
    }
  }
  
  console.log(`\n🎉 Created ${createdOrders.length}/${count} orders successfully!`);
  return createdOrders;
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'single':
        const customerIndex = args[1] ? parseInt(args[1]) : null;
        await createTestOrder(customerIndex);
        break;
        
      case 'multiple':
        const count = args[1] ? parseInt(args[1]) : 5;
        await createMultipleOrders(count);
        break;
        
      case 'customers':
        console.log('📋 Available customers:');
        realisticCustomers.forEach((customer, index) => {
          console.log(`   ${index}: ${customer.name} (${customer.email})`);
        });
        break;
        
      default:
        console.log('🛒 Order Creation Utility');
        console.log('');
        console.log('Usage:');
        console.log('  node create-test-order.js single [customerIndex]  - Create one order');
        console.log('  node create-test-order.js multiple [count]        - Create multiple orders');
        console.log('  node create-test-order.js customers               - List available customers');
        console.log('');
        console.log('Examples:');
        console.log('  node create-test-order.js single 0               - Create order for Jessica Martinez');
        console.log('  node create-test-order.js multiple 10            - Create 10 random orders');
        
        // Create one order by default
        await createTestOrder();
        break;
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

// Export functions for use in other scripts
export { createRealisticOrder, createTestOrder, createMultipleOrders, realisticCustomers };

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
