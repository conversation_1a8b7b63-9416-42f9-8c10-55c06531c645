import express from 'express';
import { showCustomers } from '../controllers/customerController.js';

const router = express.Router();

// Sample customer data (this would typically come from a database)
const customers = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", phone: "************", type: "Regular", orders: 5, totalSpent: 450.75, isNew: false },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", phone: "************", type: "VIP", orders: 12, totalSpent: 1289.50, isNew: false },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", phone: "************", type: "Wholesale", orders: 3, totalSpent: 2450.25, isNew: true },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", phone: "************", type: "Regular", orders: 1, totalSpent: 75.99, isNew: true }
];

// Canonical Customers page route
router.get('/', showCustomers);

// Customers page route
router.get('/customers', (req, res) => {
  res.render('customers', {
    customers: customers,
    totalCustomers: customers.length,
    error: null
  });
});

// API route to get customer data
router.get('/api/data', (req, res) => {
  res.json(customers);
});

export default router;
