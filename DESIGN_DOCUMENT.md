# ShopEasly Admin – Design Document

**Last updated:** June 4, 2025  
**Project Type:** Node.js/Express E-commerce Admin Panel Prototype  
**Encoding:** UTF-8  
**Primary Language:** JavaScript (ES6+), EJS for views

---

## 1. Overview

ShopEasly Admin is a prototype e-commerce admin panel for managing inventory, orders, customers, and analytics. It is designed for demonstration and internal use, not for production deployment. The system uses Node.js with Express, EJS templating, and a modular service/controller architecture. Data is stored in flat JSON files or via a MockAPI service for development.

---

## 2. Architecture

### 2.1. High-Level Structure

- **Entry Point:** `app.js`
- **Routing:** Express routers, modularized by resource (orders, customers, inventory, etc.)
- **Controllers:** Business logic for each resource
- **Services:** Data access and aggregation, including analytics
- **Views:** EJS templates for admin UI
- **Data Storage:** Flat JSON files in `/data/` or remote MockAPI
- **Authentication:** Session-based, with login/logout and middleware protection
- **Validation:** `express-validator` for API input validation
- **Security:** Helmet, rate limiting, CORS, session cookies

### 2.2. Directory Layout

- `/controllers/` – Business logic for each resource
- `/services/` – Data access, analytics, and integration with MockAPI
- `/routes/` – Express routers for each resource and API
- `/utils/` – Helper utilities (auth, email, analytics, etc.)
- `/data/` – Flat JSON files for local data storage
- `/public/` – Static assets (CSS, JS, images)
- `/views/` – EJS templates for UI
- `/middlewares/` – Custom Express middleware
- `/test/` – Test data and scripts

---

## 3. Key Components

### 3.1. Authentication & Authorization

- **File:** `utils/authHelper.js`
- **Mechanism:** Session-based authentication using Express sessions.
- **Middleware:** `ensureAuthenticated` protects admin and sensitive API routes.
- **Login/Logout:** `/login` and `/logout` routes, with hardcoded admin credentials for prototype.

### 3.2. Routing & Middleware

- **File:** `app.js`
- **Routing:** Modular, with separate routers for orders, customers, inventory, events, etc.
- **Middleware:** Helmet (CSP), CORS (restricted), rate limiting, session, body parsing, static files, EJS layouts.

### 3.3. Data Access & Services

- **File:** `services/dataService.js`
- **Functionality:** CRUD operations for products, materials, packaging, orders, and customers.
- **Storage:** Flat JSON files (e.g., `products.json`, `orders.json`) or MockAPI (for development).
- **Caching:** In-memory cache with TTL for performance.

### 3.4. Business Logic & Analytics

- **Files:** `services/dashboardService.js`, `services/customerAnalyticsService.js`
- **Functionality:** Aggregates metrics for dashboard, generates AI suggestions, computes analytics (revenue, order stats, customer retention, etc.).

### 3.5. Validation

- **Library:** `express-validator`
- **Usage:** All POST/PUT API endpoints for customers and orders use robust validation and sanitization.

### 3.6. Security

- **Helmet:** Sets secure HTTP headers and CSP.
- **CORS:** Restricted to trusted origins.
- **Rate Limiting:** Applied to all `/api/` endpoints.
- **Session Cookies:** Secure, HTTP-only, with proper expiry.
- **Input Validation:** Prevents injection and malformed data.
- **Authorization:** Only authenticated users can access admin and write APIs; public GET APIs for shopping site.

### 3.7. Views & UI

- **Engine:** EJS
- **Templates:** Dashboard, orders, customers, events, login, error pages, etc.
- **Static Assets:** CSS, JS, images in `/public/`

---

## 4. Data Model

### 4.1. Products/Inventory

- **Fields:** id, name, sku, stock, price, category, type, unit, status, supplier, imageUrl, description

### 4.2. Orders

- **Fields:** id, customerId, customerName, customerEmail, customerAddress, items (array), totalPrice, status, createdAt, trackingNumber, carrier

### 4.3. Customers

- **Fields:** id, name, email, address, phone, joinedAt, marketingPreferences, isPromoCustomer

### 4.4. Materials & Packaging

- **Fields:** id, name, stock, price, unit, type, status, notes, linkToBuy, supplier, dimensions, materialProperty

---

## 5. Security Model

- **Authentication:** Session-based, enforced via middleware.
- **Authorization:** Only authenticated users can access admin panel and write APIs.
- **Public APIs:** GET endpoints for inventory/products/orders are public for shopping site integration.
- **Input Validation:** All user input is validated and sanitized.
- **CORS:** Only trusted origins allowed.
- **Rate Limiting:** Prevents brute-force and abuse.

---

## 6. Extensibility & Maintainability

- **Modular Codebase:** Clear separation of concerns (controllers, services, routes, utils).
- **Validation:** Centralized and declarative using `express-validator`.
- **Configurable Data Source:** Can switch between flat files and MockAPI; ready for DB migration.
- **Testing:** Structure supports unit/integration testing (test directory present).

---

## 7. Limitations & Known Issues

- **Not Production-Ready:** Flat file storage is not scalable or secure.
- **No Role-Based Access:** Single admin user only.
- **No Automated Tests:** Test scripts present, but no comprehensive test suite.
- **No Real Database:** Migration to MongoDB/PostgreSQL recommended for production.
- **No Email/SMS Integration:** Notification stubs present, but not fully implemented.

---

## 8. Future Improvements

- **Database Migration:** Move from flat files to a real database.
- **Role-Based Access Control:** Support for multiple users and roles.
- **Comprehensive Testing:** Add automated unit and integration tests.
- **Production-Grade Security:** Harden session management, add CSRF protection, audit for XSS/CSRF.
- **API Documentation:** Add OpenAPI/Swagger docs for all endpoints.
- **Frontend Modernization:** Consider React/Vue for admin UI.

---

## 9. References

- [OWASP Node.js Security Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Nodejs_Security_Cheat_Sheet.html)
- [Express.js Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)
- [express-validator Documentation](https://express-validator.github.io/docs/)

---

**Prepared by:** GitHub Copilot  
**Date:** June 4, 2025
