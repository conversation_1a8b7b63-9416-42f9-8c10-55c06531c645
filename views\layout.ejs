<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><%= typeof title !== 'undefined' ? title : 'ShopEasly' %></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lobster&display=swap" rel="stylesheet">

    <!-- CSS Variables -->
    <link rel="stylesheet" href="/css/variables.css">

    <!-- Critical CSS for preventing FOUC -->
    <style>
        /* Preloader to prevent FOUC */
        .page-preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 99999;
            transition: opacity 0.3s ease;
        }
        .page-preloader.hidden {
            opacity: 0;
            pointer-events: none;
        }
        .preloader-content {
            text-align: center;
            color: white;
        }
        .preloader-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Hide content until loaded */
        body.loading .main-content {
            visibility: hidden;
        }
    </style>

    <!-- Main CSS -->
    <link rel="stylesheet" href="/css/main.css">

    <!-- Global Notification System CSS -->
    <link rel="stylesheet" href="/css/notifications.css">

    <!-- Page-specific CSS -->
    <% if (typeof pageStyles !== 'undefined') { %>
        <% if (Array.isArray(pageStyles)) { %>
            <% pageStyles.forEach(function(style) { %>
                <link rel="stylesheet" href="<%= style %>">
            <% }); %>
        <% } else { %>
            <link rel="stylesheet" href="<%= pageStyles %>">
        <% } %>
    <% } %>
</head>
<body class="loading"> <!-- Body tag will now be a flex container via main.css -->

    <!-- Page Preloader -->
    <div class="page-preloader" id="pagePreloader">
        <div class="preloader-content">
            <div class="preloader-spinner"></div>
            <h5>Loading ShopEasly...</h5>
            <p>Preparing your dashboard</p>
        </div>
    </div>

    <div class="main-content">
        <% if (typeof excludeSidebar === 'undefined' || !excludeSidebar) { %>
            <%- include('partials/_sidebar') %>
            <div class="sidebar-overlay"></div> <!-- Added for mobile sidebar -->
        <% } %>

    <!-- Content Wrapper for new layout -->
    <div class="content-wrapper <%= typeof excludeSidebar !== 'undefined' && excludeSidebar ? 'full-width' : '' %>">
        <% if (typeof excludeHeader === 'undefined' || !excludeHeader) { %>
            <%- include('partials/_header') %>
        <% } %>

        <main class="content-area"> <!-- Changed div to main for semantics -->
            <%- body %>
        </main>

        <% if (typeof excludeFooter === 'undefined' || !excludeFooter) { %>
            <%- include('partials/_footer') %>
            <!-- Ensure you have a _footer.ejs partial or remove this include -->
        <% } %>
    </div>
    </div> <!-- Close main-content -->

    <!-- Particles.js (conditionally loaded) -->
    <% if (typeof excludeParticles === 'undefined' || !excludeParticles && !(typeof currentRoute !== 'undefined' && currentRoute !== '/')) { %>
        <div id="particles-js"></div>
        <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
        <script src="/js/particles-config.js"></script>
    <% } %>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Performance Monitor (load early for accurate metrics) -->
    <script src="/js/performance-monitor.js"></script>

    <!-- Global Notification System (must load before other scripts) -->
    <script src="/js/notifications.js"></script>

    <!-- Notification Testing System (development only) -->
    <script src="/js/notification-test.js"></script>

    <!-- Preloader Management -->
    <script>
        // Hide preloader when page is fully loaded
        function hidePreloader() {
            const preloader = document.getElementById('pagePreloader');
            const body = document.body;

            if (preloader) {
                preloader.classList.add('hidden');
                body.classList.remove('loading');

                // Remove preloader from DOM after animation
                setTimeout(() => {
                    if (preloader.parentNode) {
                        preloader.remove();
                    }
                }, 300);
            }
        }

        // Hide preloader when everything is loaded
        if (document.readyState === 'complete') {
            hidePreloader();
        } else {
            window.addEventListener('load', hidePreloader);

            // Fallback timeout to prevent infinite loading
            setTimeout(hidePreloader, 5000);
        }
    </script>

    <!-- Main JavaScript -->
    <script src="/js/main.js"></script>

    <!-- Page-specific JavaScript -->
    <% if (typeof pageScripts !== 'undefined') { %>
        <% if (Array.isArray(pageScripts)) { %>
            <% pageScripts.forEach(function(script) { %>
                <script src="<%= script %>"></script>
            <% }); %>
        <% } else { %>
            <script src="<%= pageScripts %>"></script>
        <% } %>
    <% } %>
</body>
</html>
