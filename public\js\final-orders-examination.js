// Final Comprehensive Orders Page Examination
class FinalOrdersExamination {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runFinalExamination() {
    console.log('🔍 FINAL ORDERS PAGE EXAMINATION');
    console.log('================================');
    
    // 1. Page Structure & Layout
    this.examinePageStructure();
    
    // 2. Data Loading & Display
    await this.examineDataLoading();
    
    // 3. Status Column (Simple Text)
    this.examineStatusColumn();
    
    // 4. Action Buttons & Functionality
    this.examineActionButtons();
    
    // 5. Search & Filter Controls
    this.examineSearchAndFilter();
    
    // 6. Modal Dialogs
    this.examineModals();
    
    // 7. Responsive Design
    this.examineResponsiveDesign();
    
    // 8. Performance & Loading
    this.examinePerformance();
    
    // 9. Error Handling
    this.examineErrorHandling();
    
    // 10. Final Summary
    this.displayFinalSummary();
  }

  examinePageStructure() {
    console.log('🏗️ Examining page structure...');
    
    // Check main elements
    const elements = [
      { id: 'ordersTableBody', name: 'Orders Table Body' },
      { id: 'refreshBtn', name: 'Refresh Button' },
      { id: 'exportBtn', name: 'Export Button' },
      { id: 'searchInput', name: 'Search Input' },
      { id: 'statusFilter', name: 'Status Filter' }
    ];

    elements.forEach(element => {
      const el = document.getElementById(element.id);
      if (el) {
        this.addResult('✅', `${element.name} found and accessible`);
      } else {
        this.addResult('❌', `${element.name} missing`);
      }
    });

    // Check table structure
    const headers = Array.from(document.querySelectorAll('thead th'));
    const expectedHeaders = ['ORDER ID', 'CUSTOMER', 'EMAIL', 'AMOUNT', 'STATUS', 'DATE', 'ACTIONS'];
    
    if (headers.length >= 7) {
      this.addResult('✅', `Table has ${headers.length} columns`);
    } else {
      this.addResult('❌', `Table missing columns (found ${headers.length}, expected 7)`);
    }
  }

  async examineDataLoading() {
    console.log('📊 Examining data loading...');
    
    try {
      // Check API endpoint
      const response = await fetch('/api/orders?limit=5');
      if (response.ok) {
        const data = await response.json();
        const orders = data.success ? data.data : data;
        
        if (Array.isArray(orders) && orders.length > 0) {
          this.addResult('✅', `API returns ${orders.length} orders`);
          
          // Check order structure
          const firstOrder = orders[0];
          const requiredFields = ['id', 'customerName', 'email', 'amount', 'status', 'createdAt'];
          
          const missingFields = requiredFields.filter(field => !firstOrder.hasOwnProperty(field));
          if (missingFields.length === 0) {
            this.addResult('✅', 'Order data structure complete');
          } else {
            this.addResult('⚠️', `Missing fields: ${missingFields.join(', ')}`);
          }
        } else {
          this.addResult('❌', 'No orders returned from API');
        }
      } else {
        this.addResult('❌', `API error: ${response.status}`);
      }
    } catch (error) {
      this.addResult('❌', `Data loading error: ${error.message}`);
    }

    // Check if orders are displayed in table
    const orderRows = document.querySelectorAll('#ordersTableBody tr[data-order-id]');
    if (orderRows.length > 0) {
      this.addResult('✅', `${orderRows.length} orders displayed in table`);
    } else {
      this.addResult('❌', 'No orders displayed in table');
    }
  }

  examineStatusColumn() {
    console.log('📋 Examining status column...');
    
    // Check column position
    const headers = Array.from(document.querySelectorAll('thead th'));
    const statusIndex = headers.findIndex(th => th.textContent.includes('STATUS'));
    const amountIndex = headers.findIndex(th => th.textContent.includes('AMOUNT'));
    const dateIndex = headers.findIndex(th => th.textContent.includes('DATE'));

    if (statusIndex > amountIndex && statusIndex < dateIndex) {
      this.addResult('✅', 'Status column positioned correctly between Amount and Date');
    } else {
      this.addResult('❌', `Status column position incorrect: ${statusIndex}`);
    }

    // Check for simple text (no badges)
    const badges = document.querySelectorAll('#ordersTableBody .badge');
    if (badges.length === 0) {
      this.addResult('✅', 'Status displays simple text (no badges) ✨');
    } else {
      this.addResult('❌', `Found ${badges.length} badges - should be simple text only`);
    }

    // Check status text content
    const statusCells = document.querySelectorAll('#ordersTableBody tr td:nth-child(5)');
    let validStatusCount = 0;
    
    statusCells.forEach(cell => {
      const text = cell.textContent.trim();
      if (text && text !== 'Unknown' && text.length > 0) {
        validStatusCount++;
      }
    });

    if (validStatusCount === statusCells.length) {
      this.addResult('✅', 'All status cells contain valid text');
    } else {
      this.addResult('⚠️', `${validStatusCount}/${statusCells.length} status cells have valid text`);
    }
  }

  examineActionButtons() {
    console.log('⚙️ Examining action buttons...');
    
    // Check dropdown action buttons
    const actionButtons = document.querySelectorAll('[data-action]');
    const expectedActions = ['update-status', 'shipping-label', 'generate-invoice', 'track-shipment', 'customer-profile'];
    
    let foundActions = [];
    actionButtons.forEach(btn => {
      const action = btn.dataset.action;
      if (expectedActions.includes(action)) {
        foundActions.push(action);
      }
    });

    if (foundActions.length === expectedActions.length) {
      this.addResult('✅', `All ${expectedActions.length} action types found`);
    } else {
      this.addResult('⚠️', `Found ${foundActions.length}/${expectedActions.length} action types`);
    }

    // Check if OrderManager is available
    if (window.orderManager) {
      this.addResult('✅', 'OrderManager initialized and available');
    } else {
      this.addResult('❌', 'OrderManager not available');
    }
  }

  examineSearchAndFilter() {
    console.log('🔍 Examining search and filter...');
    
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    
    if (searchInput) {
      // Test search functionality
      const originalValue = searchInput.value;
      searchInput.value = 'test';
      searchInput.dispatchEvent(new Event('input'));
      searchInput.value = originalValue;
      this.addResult('✅', 'Search input functional');
    } else {
      this.addResult('❌', 'Search input not found');
    }

    if (statusFilter) {
      // Check filter options
      const options = statusFilter.querySelectorAll('option');
      if (options.length > 1) {
        this.addResult('✅', `Status filter has ${options.length} options`);
      } else {
        this.addResult('⚠️', 'Status filter has limited options');
      }
    } else {
      this.addResult('❌', 'Status filter not found');
    }
  }

  examineModals() {
    console.log('🪟 Examining modal dialogs...');
    
    const modals = [
      { id: 'statusUpdateModal', name: 'Status Update Modal' },
      { id: 'shippingModal', name: 'Shipping Modal' },
      { id: 'trackingModal', name: 'Tracking Modal' },
      { id: 'customerProfileModal', name: 'Customer Profile Modal' }
    ];

    modals.forEach(modal => {
      const element = document.getElementById(modal.id);
      if (element) {
        this.addResult('✅', `${modal.name} found`);
        
        // Check if modal can be initialized
        try {
          const bsModal = new bootstrap.Modal(element);
          this.addResult('✅', `${modal.name} can be initialized`);
        } catch (error) {
          this.addResult('❌', `${modal.name} initialization error`);
        }
      } else {
        this.addResult('❌', `${modal.name} not found`);
      }
    });
  }

  examineResponsiveDesign() {
    console.log('📱 Examining responsive design...');
    
    // Check if table is responsive
    const table = document.querySelector('.table-responsive');
    if (table) {
      this.addResult('✅', 'Table has responsive wrapper');
    } else {
      this.addResult('⚠️', 'Table may not be fully responsive');
    }

    // Check mobile-specific elements
    const mobileHidden = document.querySelectorAll('.d-none.d-md-table-cell');
    if (mobileHidden.length > 0) {
      this.addResult('✅', `${mobileHidden.length} columns hidden on mobile`);
    } else {
      this.addResult('ℹ️', 'No mobile-specific column hiding detected');
    }
  }

  examinePerformance() {
    console.log('⚡ Examining performance...');
    
    const endTime = Date.now();
    const loadTime = endTime - this.startTime;
    
    if (loadTime < 3000) {
      this.addResult('✅', `Page examination completed in ${loadTime}ms`);
    } else {
      this.addResult('⚠️', `Page examination took ${loadTime}ms (may be slow)`);
    }

    // Check if notifications system is available
    if (window.notifications) {
      this.addResult('✅', 'Notification system available');
    } else {
      this.addResult('⚠️', 'Notification system not available');
    }
  }

  examineErrorHandling() {
    console.log('🛡️ Examining error handling...');
    
    // Check if error handling functions exist
    if (window.orderManager && typeof window.orderManager.showNotification === 'function') {
      this.addResult('✅', 'Error notification system available');
    } else {
      this.addResult('⚠️', 'Error notification system may be limited');
    }

    // Check console for any errors
    const hasConsoleErrors = window.console && window.console.error;
    if (hasConsoleErrors) {
      this.addResult('✅', 'Console error handling available');
    }
  }

  addResult(status, message) {
    this.results.push({ status, message, timestamp: Date.now() });
    console.log(`${status} ${message}`);
  }

  displayFinalSummary() {
    const endTime = Date.now();
    const totalTime = endTime - this.startTime;
    
    console.log('\n🎯 FINAL ORDERS PAGE EXAMINATION SUMMARY');
    console.log('========================================');
    
    const passed = this.results.filter(r => r.status === '✅').length;
    const failed = this.results.filter(r => r.status === '❌').length;
    const warnings = this.results.filter(r => r.status === '⚠️').length;
    const info = this.results.filter(r => r.status === 'ℹ️').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`ℹ️ Info: ${info}`);
    console.log(`⏱️ Total Time: ${totalTime}ms`);
    console.log(`📊 Success Rate: ${Math.round((passed / this.results.length) * 100)}%`);
    
    // Overall assessment
    if (failed === 0 && warnings <= 2) {
      console.log('\n🎉 ORDERS PAGE STATUS: EXCELLENT ✨');
      console.log('Ready to move on to other page improvements!');
    } else if (failed <= 2 && warnings <= 5) {
      console.log('\n✅ ORDERS PAGE STATUS: GOOD');
      console.log('Minor issues present but page is functional');
    } else {
      console.log('\n⚠️ ORDERS PAGE STATUS: NEEDS ATTENTION');
      console.log('Several issues found that should be addressed');
    }

    // Show notification
    if (window.notifications) {
      const successRate = Math.round((passed / this.results.length) * 100);
      window.notifications.showToast(
        `Orders page examination complete: ${successRate}% success rate (${passed}/${this.results.length} passed)`,
        failed === 0 ? 'success' : failed <= 2 ? 'warning' : 'error',
        'Final Examination Complete'
      );
    }

    return {
      passed,
      failed,
      warnings,
      info,
      total: this.results.length,
      successRate: Math.round((passed / this.results.length) * 100),
      totalTime,
      status: failed === 0 && warnings <= 2 ? 'EXCELLENT' : failed <= 2 ? 'GOOD' : 'NEEDS_ATTENTION'
    };
  }
}

// Auto-run examination when page loads (if in test mode)
if (window.location.search.includes('final-exam=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const examination = new FinalOrdersExamination();
      examination.runFinalExamination();
    }, 3000);
  });
}

// Make examination available globally
window.FinalOrdersExamination = FinalOrdersExamination;

console.log('🔍 Final Orders Examination loaded. Run with: new FinalOrdersExamination().runFinalExamination();');
