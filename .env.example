# Application Settings
PORT=10000
NODE_ENV=development

# CORS Configuration
PRODUCTION_ORIGIN=https://shop-easly-admin.onrender.com
FRONTEND_URL=https://shop-easly-admin.onrender.com

# Production API Configuration
SHOPPING_SITE_API_KEY=your-secure-api-key-here

# OpenAI API Configuration
OPENAI_API_KEY=OpenAI_API_Key

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Mock API Configuration
MOCK_API_URL=https://681a3a3c1ac1155635084e35.mockapi.io

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=shop_easly

# JWT Configuration
JWT_SECRET=your_jwt_secret

# Session Configuration
SESSION_SECRET=your_session_secret

# Canva API Configuration
# Get these from https://www.canva.com/developers/
CANVA_CLIENT_ID=your_canva_client_id_here
CANVA_CLIENT_SECRET=your_canva_client_secret_here
CANVA_REDIRECT_URI=http://localhost:3000/api/canva/callback

