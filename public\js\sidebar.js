// Sidebar functionality - Cleaned up and optimized
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const contentWrapper = document.querySelector('.content-wrapper');
    const sidebarOverlay = document.querySelector('.sidebar-overlay');

    // Initialize sidebar state
    initializeSidebar();

    // Toggle sidebar on button click (desktop sidebar toggle)
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSidebar();
        });
    }

    // Toggle sidebar on mobile menu button click (header mobile toggle)
    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSidebar();
        });
    }

    // Close sidebar when clicking overlay (mobile)
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            closeSidebar();
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        handleResize();
    });

    // Set active navigation item
    setActiveNavItem();

    function initializeSidebar() {
        // Check if we're on mobile
        if (window.innerWidth <= 768) {
            sidebar?.classList.add('mobile-hidden');
        } else {
            // Check localStorage for sidebar state
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar?.classList.add('collapsed');
                mainContent?.classList.add('expanded');
                contentWrapper?.classList.add('sidebar-collapsed');
            }
        }
    }

    function toggleSidebar() {
        if (window.innerWidth <= 768) {
            // Mobile behavior
            if (sidebar?.classList.contains('mobile-open')) {
                closeSidebar();
            } else {
                openSidebar();
            }
        } else {
            // Desktop behavior
            if (sidebar?.classList.contains('collapsed')) {
                expandSidebar();
            } else {
                collapseSidebar();
            }
        }
    }

    function openSidebar() {
        sidebar?.classList.add('mobile-open');
        sidebar?.classList.remove('mobile-hidden');
        sidebarOverlay?.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeSidebar() {
        sidebar?.classList.remove('mobile-open');
        sidebarOverlay?.classList.remove('active');
        document.body.style.overflow = '';

        if (window.innerWidth <= 768) {
            sidebar?.classList.add('mobile-hidden');
        }
    }

    function collapseSidebar() {
        sidebar?.classList.add('collapsed');
        mainContent?.classList.add('expanded');
        contentWrapper?.classList.add('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', 'true');
    }

    function expandSidebar() {
        sidebar?.classList.remove('collapsed');
        mainContent?.classList.remove('expanded');
        contentWrapper?.classList.remove('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', 'false');
    }

    function handleResize() {
        if (window.innerWidth <= 768) {
            // Mobile mode
            sidebar?.classList.remove('collapsed');
            mainContent?.classList.remove('expanded');
            contentWrapper?.classList.remove('sidebar-collapsed');

            if (!sidebar?.classList.contains('mobile-open')) {
                sidebar?.classList.add('mobile-hidden');
            }
        } else {
            // Desktop mode
            sidebar?.classList.remove('mobile-open', 'mobile-hidden');
            sidebarOverlay?.classList.remove('active');
            document.body.style.overflow = '';

            // Restore collapsed state from localStorage
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                collapseSidebar();
            }
        }
    }

    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');

            const href = link.getAttribute('href');
            if (href === currentPath) {
                link.classList.add('active');
            }
        });
    }
});

// Global function for backward compatibility
window.initializeSidebar = function() {
    // This function is now handled by the DOMContentLoaded event above
    console.log('Sidebar initialized via DOMContentLoaded');
};
