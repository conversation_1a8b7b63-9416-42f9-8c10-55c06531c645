// Simple Status Display Verification
class SimpleStatusCheck {
  constructor() {
    this.results = [];
  }

  runCheck() {
    console.log('🔍 Checking simple status display...');
    
    // Check 1: Verify no badges exist
    this.checkNoBadges();
    
    // Check 2: Verify status text is displayed
    this.checkStatusText();
    
    // Check 3: Verify column position
    this.checkColumnPosition();
    
    // Display results
    this.displayResults();
  }

  checkNoBadges() {
    const badges = document.querySelectorAll('#ordersTableBody .badge');
    
    if (badges.length === 0) {
      this.addResult('✅', 'No badges found - simple text display confirmed');
    } else {
      this.addResult('❌', `Found ${badges.length} badges - badges should be removed`);
    }
  }

  checkStatusText() {
    const statusCells = document.querySelectorAll('#ordersTableBody tr td:nth-child(5)');
    
    if (statusCells.length > 0) {
      this.addResult('✅', `Found ${statusCells.length} status cells with text`);
      
      // Check if cells contain text (not empty)
      let cellsWithText = 0;
      statusCells.forEach(cell => {
        if (cell.textContent.trim().length > 0) {
          cellsWithText++;
        }
      });
      
      if (cellsWithText === statusCells.length) {
        this.addResult('✅', 'All status cells contain text');
      } else {
        this.addResult('⚠️', `${cellsWithText}/${statusCells.length} cells have text`);
      }
    } else {
      this.addResult('❌', 'No status cells found');
    }
  }

  checkColumnPosition() {
    const headers = Array.from(document.querySelectorAll('thead th'));
    const statusIndex = headers.findIndex(th => th.textContent.includes('STATUS'));
    const amountIndex = headers.findIndex(th => th.textContent.includes('AMOUNT'));
    const dateIndex = headers.findIndex(th => th.textContent.includes('DATE'));

    if (statusIndex > amountIndex && statusIndex < dateIndex) {
      this.addResult('✅', 'Status column correctly positioned between Amount and Date');
    } else {
      this.addResult('❌', `Status column position incorrect: ${statusIndex}`);
    }
  }

  addResult(status, message) {
    this.results.push({ status, message });
    console.log(`${status} ${message}`);
  }

  displayResults() {
    console.log('\n🔍 SIMPLE STATUS CHECK RESULTS');
    console.log('==============================');
    
    const passed = this.results.filter(r => r.status === '✅').length;
    const failed = this.results.filter(r => r.status === '❌').length;
    const warnings = this.results.filter(r => r.status === '⚠️').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    
    if (failed === 0) {
      console.log('🎉 Status display successfully simplified!');
      
      if (window.notifications) {
        window.notifications.showToast(
          'Status display successfully simplified - badges removed, text only',
          'success',
          'Status Update Complete'
        );
      }
    } else {
      console.log('⚠️ Some issues found with status display');
    }
  }
}

// Auto-run check when page loads (if in test mode)
if (window.location.search.includes('simple-status=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const check = new SimpleStatusCheck();
      check.runCheck();
    }, 2000);
  });
}

// Make check available globally
window.SimpleStatusCheck = SimpleStatusCheck;

console.log('🔍 Simple Status Check loaded. Run with: new SimpleStatusCheck().runCheck();');
