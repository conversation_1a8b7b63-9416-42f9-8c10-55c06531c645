/**
 * ShopEasly Integration Library for Your Shopping Website
 * Drop-in solution for connecting your existing shopping site to ShopEasly Admin
 */

class ShopEaslyAPI {
  constructor(config = {}) {
    this.apiKey = config.apiKey || process.env.SHOPEASLY_API_KEY;
    this.baseUrl = config.baseUrl || 'https://shop-easly-admin.onrender.com';
    this.timeout = config.timeout || 10000; // 10 seconds
    this.retryAttempts = config.retryAttempts || 3;
    this.debug = config.debug || false;
    
    if (!this.apiKey) {
      throw new Error('ShopEasly API key is required');
    }
  }

  /**
   * Make authenticated API request with retry logic
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseUrl}/api/production${endpoint}`;
    const config = {
      timeout: this.timeout,
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    let lastError;
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        if (this.debug) {
          console.log(`[ShopEasly] ${config.method || 'GET'} ${url} (attempt ${attempt})`);
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        const response = await fetch(url, {
          ...config,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);

        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${result.message || response.statusText}`);
        }

        if (!result.success) {
          throw new Error(result.message || 'API request failed');
        }

        if (this.debug) {
          console.log(`[ShopEasly] Success:`, result.data);
        }

        return result.data;

      } catch (error) {
        lastError = error;
        
        if (this.debug) {
          console.warn(`[ShopEasly] Attempt ${attempt} failed:`, error.message);
        }

        // Don't retry on client errors (4xx)
        if (error.message.includes('HTTP 4')) {
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < this.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError;
  }

  /**
   * Check if a single product is in stock
   */
  async checkStock(productId, quantity = 1) {
    try {
      const result = await this.makeRequest(`/inventory/check/${productId}?quantity=${quantity}`);
      
      return {
        productId: result.productId,
        productName: result.productName,
        inStock: result.isInStock,
        availableStock: result.availableStock,
        requestedQuantity: result.requestedQuantity,
        stockStatus: result.stockStatus, // 'in_stock', 'low_stock', 'out_of_stock'
        canFulfill: result.canFulfillOrder
      };
    } catch (error) {
      if (this.debug) {
        console.error(`[ShopEasly] Stock check failed for ${productId}:`, error);
      }
      
      return {
        productId,
        inStock: false,
        error: error.message,
        canFulfill: false
      };
    }
  }

  /**
   * Validate entire shopping cart
   */
  async validateCart(cartItems) {
    try {
      const products = cartItems.map(item => ({
        productId: item.productId || item.id,
        quantity: item.quantity || 1
      }));

      const result = await this.makeRequest('/inventory/bulk-check', {
        method: 'POST',
        body: JSON.stringify({ products })
      });

      return {
        canFulfillOrder: result.canFulfillEntireOrder,
        allInStock: result.allInStock,
        products: result.products.map(p => ({
          productId: p.productId,
          productName: p.productName,
          inStock: p.isInStock,
          availableStock: p.availableStock,
          requestedQuantity: p.requestedQuantity,
          canFulfill: p.canFulfillOrder
        })),
        checkedAt: result.checkedAt
      };
    } catch (error) {
      if (this.debug) {
        console.error('[ShopEasly] Cart validation failed:', error);
      }
      
      return {
        canFulfillOrder: false,
        error: error.message,
        products: cartItems.map(item => ({
          productId: item.productId || item.id,
          inStock: false,
          error: 'Validation failed'
        }))
      };
    }
  }

  /**
   * Submit order to ShopEasly Admin
   */
  async submitOrder(orderData) {
    try {
      // Normalize order data
      const normalizedOrder = {
        orderId: orderData.orderId || orderData.id,
        customerName: orderData.customer?.name || orderData.customerName,
        customerEmail: orderData.customer?.email || orderData.customerEmail,
        customerPhone: orderData.customer?.phone || orderData.customerPhone,
        customerAddress: this.formatAddress(orderData.customer?.address || orderData.customerAddress),
        items: this.normalizeItems(orderData.items),
        totalPrice: parseFloat(orderData.totalPrice || orderData.total || 0),
        paymentStatus: orderData.paymentStatus || 'completed',
        shippingMethod: orderData.shippingMethod || orderData.shipping?.method || 'standard',
        notes: orderData.notes || orderData.specialInstructions || null
      };

      // Validate required fields
      this.validateOrderData(normalizedOrder);

      const result = await this.makeRequest('/orders', {
        method: 'POST',
        body: JSON.stringify(normalizedOrder)
      });

      return {
        orderId: result.orderId,
        status: result.status,
        message: result.message,
        success: true
      };
    } catch (error) {
      if (this.debug) {
        console.error('[ShopEasly] Order submission failed:', error);
      }
      
      return {
        success: false,
        error: error.message,
        orderId: orderData.orderId || orderData.id
      };
    }
  }

  /**
   * Get order status for customer tracking
   */
  async getOrderStatus(orderId) {
    try {
      const result = await this.makeRequest(`/orders/${orderId}/status`);
      
      return {
        orderId: result.orderId,
        status: result.status,
        trackingNumber: result.trackingNumber,
        estimatedDelivery: result.estimatedDelivery,
        lastUpdated: result.lastUpdated,
        statusHistory: result.statusHistory || []
      };
    } catch (error) {
      if (this.debug) {
        console.error(`[ShopEasly] Status check failed for ${orderId}:`, error);
      }
      
      return {
        orderId,
        error: error.message,
        status: 'unknown'
      };
    }
  }

  /**
   * Test API connection
   */
  async testConnection() {
    try {
      const response = await fetch(`${this.baseUrl}/api/production/health`);
      const result = await response.json();
      
      return {
        connected: result.success,
        message: result.message,
        timestamp: result.timestamp,
        version: result.version
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }

  // Helper methods
  formatAddress(address) {
    if (typeof address === 'string') {
      return address;
    }
    
    if (typeof address === 'object' && address) {
      const parts = [
        address.street || address.line1,
        address.line2,
        address.city,
        address.state || address.province,
        address.zipCode || address.postalCode,
        address.country
      ].filter(Boolean);
      
      return parts.join(', ');
    }
    
    return null;
  }

  normalizeItems(items) {
    if (!Array.isArray(items)) {
      return [];
    }
    
    return items.map(item => ({
      productId: item.productId || item.id || item.sku,
      productName: item.productName || item.name || item.title,
      quantity: parseInt(item.quantity) || 1,
      price: parseFloat(item.price) || 0,
      sku: item.sku || item.productId || item.id
    }));
  }

  validateOrderData(orderData) {
    const required = ['orderId', 'customerName', 'customerEmail', 'items', 'totalPrice'];
    const missing = required.filter(field => !orderData[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }
    
    if (!Array.isArray(orderData.items) || orderData.items.length === 0) {
      throw new Error('Order must contain at least one item');
    }
    
    if (orderData.totalPrice <= 0) {
      throw new Error('Order total must be greater than 0');
    }
  }
}

// Convenience wrapper for common e-commerce platforms
class ShopEaslyEcommerce extends ShopEaslyAPI {
  /**
   * Shopify-style integration
   */
  async validateShopifyCart(cart) {
    const cartItems = cart.items.map(item => ({
      productId: item.variant_id.toString(),
      quantity: item.quantity
    }));
    
    return this.validateCart(cartItems);
  }

  async submitShopifyOrder(order) {
    const orderData = {
      orderId: order.order_number || order.id,
      customerName: `${order.customer.first_name} ${order.customer.last_name}`,
      customerEmail: order.customer.email,
      customerPhone: order.customer.phone,
      customerAddress: order.shipping_address,
      items: order.line_items.map(item => ({
        productId: item.variant_id.toString(),
        productName: item.name,
        quantity: item.quantity,
        price: parseFloat(item.price),
        sku: item.sku
      })),
      totalPrice: parseFloat(order.total_price),
      paymentStatus: order.financial_status === 'paid' ? 'completed' : 'pending',
      shippingMethod: order.shipping_lines[0]?.title || 'standard'
    };
    
    return this.submitOrder(orderData);
  }

  /**
   * WooCommerce-style integration
   */
  async submitWooCommerceOrder(order) {
    const orderData = {
      orderId: order.id.toString(),
      customerName: `${order.billing.first_name} ${order.billing.last_name}`,
      customerEmail: order.billing.email,
      customerPhone: order.billing.phone,
      customerAddress: order.shipping,
      items: order.line_items.map(item => ({
        productId: item.product_id.toString(),
        productName: item.name,
        quantity: item.quantity,
        price: parseFloat(item.price),
        sku: item.sku
      })),
      totalPrice: parseFloat(order.total),
      paymentStatus: order.status === 'processing' ? 'completed' : 'pending',
      shippingMethod: order.shipping_lines[0]?.method_title || 'standard'
    };
    
    return this.submitOrder(orderData);
  }
}

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
  // Node.js
  module.exports = { ShopEaslyAPI, ShopEaslyEcommerce };
} else if (typeof window !== 'undefined') {
  // Browser
  window.ShopEaslyAPI = ShopEaslyAPI;
  window.ShopEaslyEcommerce = ShopEaslyEcommerce;
}

// Example usage:
/*
// Initialize
const shopEasly = new ShopEaslyAPI({
  apiKey: 'your-api-key-here',
  baseUrl: 'https://shop-easly-admin.onrender.com',
  debug: true
});

// Test connection
const health = await shopEasly.testConnection();
console.log('API Health:', health);

// Check stock before adding to cart
const stockCheck = await shopEasly.checkStock('PRODUCT_123', 2);
if (!stockCheck.inStock) {
  alert('Sorry, this item is out of stock');
}

// Validate cart before checkout
const cartValidation = await shopEasly.validateCart(cartItems);
if (!cartValidation.canFulfillOrder) {
  showOutOfStockItems(cartValidation.products);
}

// Submit order after payment
const orderResult = await shopEasly.submitOrder({
  orderId: 'WEB_' + Date.now(),
  customer: {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Main St, City, State 12345'
  },
  items: cartItems,
  totalPrice: 99.99,
  paymentStatus: 'completed'
});

if (orderResult.success) {
  showOrderConfirmation(orderResult.orderId);
} else {
  handleOrderError(orderResult.error);
}
*/
