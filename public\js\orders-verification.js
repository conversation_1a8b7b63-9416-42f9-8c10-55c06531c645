// Orders Page Verification Script
// This script verifies that all JavaScript files are loading correctly and all functionality is working

class OrdersVerification {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runVerification() {
    console.log('🔍 Starting Orders Page Verification...');
    
    // Test 1: Check if all required scripts are loaded
    this.checkScriptLoading();
    
    // Test 2: Check if DOM elements exist
    this.checkDOMElements();
    
    // Test 3: Check if OrderManager is initialized
    await this.checkOrderManager();
    
    // Test 4: Check if API endpoints are working
    await this.checkAPIEndpoints();
    
    // Test 5: Check if notifications system is working
    this.checkNotificationSystem();
    
    // Test 6: Check if Bootstrap components are working
    this.checkBootstrapComponents();
    
    // Display results
    this.displayResults();
  }

  checkScriptLoading() {
    console.log('📜 Checking script loading...');
    
    const requiredGlobals = [
      { name: 'bootstrap', description: 'Bootstrap JavaScript' },
      { name: 'notifications', description: 'Notification System' },
      { name: 'orderManager', description: 'Order Manager' }
    ];

    requiredGlobals.forEach(global => {
      if (window[global.name]) {
        this.addResult('✅', `${global.description} loaded successfully`);
      } else {
        this.addResult('❌', `${global.description} not found`);
      }
    });

    // Check if sidebar functions are available
    if (typeof window.initializeSidebar === 'function') {
      this.addResult('✅', 'Sidebar functions available');
    } else {
      this.addResult('❌', 'Sidebar functions not available');
    }
  }

  checkDOMElements() {
    console.log('🏗️ Checking DOM elements...');
    
    const requiredElements = [
      'ordersTableBody',
      'refreshBtn', 
      'exportBtn',
      'searchInput',
      'statusFilter',
      'statusUpdateModal',
      'shippingModal',
      'trackingModal',
      'customerProfileModal'
    ];

    requiredElements.forEach(elementId => {
      const element = document.getElementById(elementId);
      if (element) {
        this.addResult('✅', `Element #${elementId} found`);
      } else {
        this.addResult('❌', `Element #${elementId} missing`);
      }
    });
  }

  async checkOrderManager() {
    console.log('⚙️ Checking OrderManager...');
    
    // Wait a bit for OrderManager to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (window.orderManager) {
      this.addResult('✅', 'OrderManager initialized');
      
      // Check if orders are loaded
      if (window.orderManager.orders && window.orderManager.orders.length > 0) {
        this.addResult('✅', `${window.orderManager.orders.length} orders loaded`);
      } else {
        this.addResult('⚠️', 'No orders loaded yet');
      }
    } else {
      this.addResult('❌', 'OrderManager not initialized');
    }
  }

  async checkAPIEndpoints() {
    console.log('🌐 Checking API endpoints...');
    
    try {
      // Test orders endpoint
      const ordersResponse = await fetch('/api/orders?limit=1');
      if (ordersResponse.ok) {
        this.addResult('✅', 'Orders API endpoint working');
      } else {
        this.addResult('❌', `Orders API error: ${ordersResponse.status}`);
      }
    } catch (error) {
      this.addResult('❌', `Orders API error: ${error.message}`);
    }
  }

  checkNotificationSystem() {
    console.log('🔔 Checking notification system...');
    
    if (window.notifications) {
      try {
        // Test notification
        window.notifications.showToast('Verification test notification', 'info', 'Test', 2000);
        this.addResult('✅', 'Notification system working');
      } catch (error) {
        this.addResult('❌', `Notification error: ${error.message}`);
      }
    } else {
      this.addResult('❌', 'Notification system not available');
    }
  }

  checkBootstrapComponents() {
    console.log('🎨 Checking Bootstrap components...');
    
    if (typeof bootstrap !== 'undefined') {
      this.addResult('✅', 'Bootstrap loaded');
      
      // Check if modals can be initialized
      const testModal = document.getElementById('statusUpdateModal');
      if (testModal) {
        try {
          const modal = new bootstrap.Modal(testModal);
          this.addResult('✅', 'Bootstrap modals working');
        } catch (error) {
          this.addResult('❌', `Bootstrap modal error: ${error.message}`);
        }
      }
      
      // Check if dropdowns work
      const dropdowns = document.querySelectorAll('.dropdown-toggle');
      if (dropdowns.length > 0) {
        this.addResult('✅', `${dropdowns.length} dropdown menus found`);
      } else {
        this.addResult('⚠️', 'No dropdown menus found');
      }
    } else {
      this.addResult('❌', 'Bootstrap not loaded');
    }
  }

  addResult(status, message) {
    this.results.push({ status, message, timestamp: Date.now() });
    console.log(`${status} ${message}`);
  }

  displayResults() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    console.log('\n🔍 ORDERS PAGE VERIFICATION RESULTS');
    console.log('=====================================');
    
    const passed = this.results.filter(r => r.status === '✅').length;
    const failed = this.results.filter(r => r.status === '❌').length;
    const warnings = this.results.filter(r => r.status === '⚠️').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`⏱️ Duration: ${duration}ms`);
    console.log(`📊 Success Rate: ${Math.round((passed / this.results.length) * 100)}%`);
    
    // Show detailed results
    console.log('\nDetailed Results:');
    this.results.forEach(result => {
      console.log(`${result.status} ${result.message}`);
    });

    // Show summary notification
    if (window.notifications) {
      const successRate = Math.round((passed / this.results.length) * 100);
      const type = successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error';
      
      window.notifications.showToast(
        `Orders page verification complete: ${successRate}% success rate (${passed}/${this.results.length} passed)`,
        type,
        'Verification Complete'
      );
    }

    // Return results for programmatic access
    return {
      passed,
      failed,
      warnings,
      total: this.results.length,
      successRate: Math.round((passed / this.results.length) * 100),
      duration,
      details: this.results
    };
  }
}

// Auto-run verification when page loads (if in test mode)
if (window.location.search.includes('verify=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const verification = new OrdersVerification();
      verification.runVerification();
    }, 2000); // Wait for everything to load
  });
}

// Make verification available globally
window.OrdersVerification = OrdersVerification;

// Add manual verification trigger
console.log('🔍 Orders Verification loaded. Run verification manually with:');
console.log('const verification = new OrdersVerification(); verification.runVerification();');
