// This file has been replaced by ai-helper.js for canonical AI Helper functionality. Please use ai-helper.js.

// Modern AI Assistant Chat UI Logic
// Handles chat send, message rendering, conversation switching, and UI interactivity

document.addEventListener('DOMContentLoaded', () => {
  // Elements
  const chatMessages = document.querySelector('.chat-messages');
  const chatInput = document.querySelector('.chat-input');
  const sendBtn = document.querySelector('.input-action-btn.send-btn');
  const newChatBtn = document.querySelector('.new-chat-btn');
  const conversationList = document.querySelector('.conversation-list');
  const modelOptions = document.querySelectorAll('.model-option');

  // State
  let conversations = [];
  let currentConversationId = null;
  let aiModel = 'gpt-4';

  // Helpers
  function formatTime(date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  function renderMessage(message, isUser) {
    const msgDiv = document.createElement('div');
    msgDiv.className = 'message' + (isUser ? ' user' : '');
    msgDiv.innerHTML = `
      <div class="message-avatar">${isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>'}</div>
      <div class="message-content">
        <div class="message-bubble-pointer"></div>
        <div class="message-text">${message.text}</div>
        <div class="message-time">${formatTime(new Date(message.time))}</div>
      </div>
    `;
    chatMessages.appendChild(msgDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }

  function renderConversationList() {
    conversationList.innerHTML = '';
    conversations.forEach(conv => {
      const li = document.createElement('li');
      li.className = 'conversation-item' + (conv.id === currentConversationId ? ' active' : '');
      li.innerHTML = `
        <div class="conversation-icon"><i class="fas fa-comments"></i></div>
        <div class="conversation-info">
          <div class="conversation-name">${conv.name}</div>
          <div class="conversation-preview">${conv.messages.length ? conv.messages[conv.messages.length-1].text : 'No messages yet'}</div>
        </div>
      `;
      li.onclick = () => switchConversation(conv.id);
      conversationList.appendChild(li);
    });
  }

  function renderChat() {
    chatMessages.innerHTML = '';
    const conv = conversations.find(c => c.id === currentConversationId);
    if (!conv) return;
    conv.messages.forEach(msg => renderMessage(msg, msg.user));
  }

  function switchConversation(id) {
    currentConversationId = id;
    renderConversationList();
    renderChat();
  }

  function createNewConversation() {
    const id = 'conv-' + Date.now();
    const name = 'Chat ' + (conversations.length + 1);
    conversations.unshift({ id, name, messages: [] });
    currentConversationId = id;
    renderConversationList();
    renderChat();
  }

  function sendMessage() {
    const text = chatInput.value.trim();
    if (!text) return;
    const conv = conversations.find(c => c.id === currentConversationId);
    if (!conv) return;
    const userMsg = { text, user: true, time: new Date().toISOString() };
    conv.messages.push(userMsg);
    renderMessage(userMsg, true);
    chatInput.value = '';
    // Simulate AI response (replace with real API call)
    setTimeout(() => {
      const aiMsg = { text: `AI (${aiModel}): This is a simulated response to: "${text}"`, user: false, time: new Date().toISOString() };
      conv.messages.push(aiMsg);
      renderMessage(aiMsg, false);
    }, 900);
  }

  // Model selection
  modelOptions.forEach(opt => {
    opt.addEventListener('click', () => {
      modelOptions.forEach(o => o.classList.remove('active'));
      opt.classList.add('active');
      aiModel = opt.dataset.model;
    });
  });

  // Event listeners
  sendBtn.addEventListener('click', sendMessage);
  chatInput.addEventListener('keydown', e => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });
  newChatBtn.addEventListener('click', createNewConversation);

  // Initial setup
  createNewConversation();
});
