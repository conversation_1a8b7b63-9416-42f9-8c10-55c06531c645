#!/usr/bin/env node

/**
 * Real-World MockAPI Order Testing Script
 * Tests order creation, retrieval, and management functions
 */

import fetch from 'node-fetch';
import mockApiService from './services/mockApiService.js';

// Configuration
const MOCKAPI_URL = 'https://681a3a3c1ac1155635084e35.mockapi.io';
const LOCAL_SERVER = 'http://localhost:3000';

// Real-world test order data
const realWorldOrder = {
  customerName: "<PERSON>",
  customerEmail: "<EMAIL>",
  customerPhone: "(*************",
  customerAddress: "1247 Sunset Boulevard, Los Angeles, CA 90026",
  customerId: "CUST_2024_001",
  items: [
    {
      productId: "TSH_COTTON_BLK_M",
      name: "Premium Cotton T-Shirt - Black - Medium",
      price: 24.99,
      quantity: 2,
      sku: "TSH-CTN-BLK-M",
      category: "Apparel"
    },
    {
      productId: "MUG_CERAMIC_WHT_15OZ",
      name: "Ceramic Coffee Mug - White - 15oz",
      price: 12.50,
      quantity: 1,
      sku: "MUG-CER-WHT-15",
      category: "Drinkware"
    },
    {
      productId: "STK_VINYL_LOGO_3IN",
      name: "Logo Vinyl Sticker - 3 inch",
      price: 3.99,
      quantity: 5,
      sku: "STK-VIN-LOGO-3",
      category: "Accessories"
    }
  ],
  totalPrice: 82.43,
  subtotal: 74.93,
  tax: 5.99,
  shipping: 1.51,
  status: "pending",
  paymentMethod: "Credit Card",
  paymentStatus: "completed",
  shippingMethod: "Standard Ground",
  shippingCarrier: "UPS",
  trackingNumber: null,
  notes: "Customer requested eco-friendly packaging",
  orderSource: "website",
  discountCode: "WELCOME10",
  discountAmount: 7.49,
  createdAt: new Date().toISOString(),
  estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days from now
};

// Additional test orders for variety
const additionalTestOrders = [
  {
    customerName: "Robert Chen",
    customerEmail: "<EMAIL>",
    customerPhone: "(*************",
    customerAddress: "456 Market Street, Suite 200, San Francisco, CA 94102",
    customerId: "CUST_2024_002",
    items: [
      {
        productId: "HOD_FLEECE_GRY_L",
        name: "Fleece Hoodie - Gray - Large",
        price: 45.00,
        quantity: 1,
        sku: "HOD-FLE-GRY-L",
        category: "Apparel"
      }
    ],
    totalPrice: 52.25,
    subtotal: 45.00,
    tax: 3.60,
    shipping: 3.65,
    status: "processing",
    paymentMethod: "PayPal",
    paymentStatus: "completed",
    shippingMethod: "Express",
    shippingCarrier: "FedEx",
    orderSource: "mobile_app",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
  },
  {
    customerName: "Amanda Thompson",
    customerEmail: "<EMAIL>",
    customerPhone: "(*************",
    customerAddress: "789 Pine Ridge Drive, Denver, CO 80203",
    customerId: "CUST_2024_003",
    items: [
      {
        productId: "BAG_TOTE_CAN_LRG",
        name: "Canvas Tote Bag - Large",
        price: 18.99,
        quantity: 2,
        sku: "BAG-TOT-CAN-L",
        category: "Bags"
      },
      {
        productId: "PIN_ENAMEL_LOGO",
        name: "Enamel Logo Pin",
        price: 8.50,
        quantity: 3,
        sku: "PIN-ENA-LOGO",
        category: "Accessories"
      }
    ],
    totalPrice: 67.48,
    subtotal: 63.48,
    tax: 4.00,
    shipping: 0.00, // Free shipping promotion
    status: "shipped",
    paymentMethod: "Credit Card",
    paymentStatus: "completed",
    shippingMethod: "Standard Ground",
    shippingCarrier: "USPS",
    trackingNumber: "9400111899562537866732",
    orderSource: "website",
    discountCode: "FREESHIP",
    discountAmount: 0.00,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days ago
  }
];

class MockAPIOrderTester {
  constructor() {
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const emoji = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      test: '🧪'
    }[type] || 'ℹ️';
    
    console.log(`${emoji} [${timestamp}] ${message}`);
  }

  async testDirectMockAPI() {
    this.log('Testing direct MockAPI connection...', 'test');
    
    try {
      // Test GET orders
      const response = await fetch(`${MOCKAPI_URL}/orders`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const orders = await response.json();
      this.log(`Successfully fetched ${orders.length} orders from MockAPI`, 'success');
      
      // Test POST new order
      const createResponse = await fetch(`${MOCKAPI_URL}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(realWorldOrder)
      });
      
      if (!createResponse.ok) {
        throw new Error(`Failed to create order: HTTP ${createResponse.status}`);
      }
      
      const newOrder = await createResponse.json();
      this.log(`Successfully created order ${newOrder.id} for ${newOrder.customerName}`, 'success');
      
      return newOrder;
      
    } catch (error) {
      this.log(`Direct MockAPI test failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async testMockAPIService() {
    this.log('Testing MockAPI service functions...', 'test');
    
    try {
      // Test getOrders
      const orders = await mockApiService.getOrders();
      this.log(`MockAPI service fetched ${orders.length} orders`, 'success');
      
      // Test createOrder
      const newOrder = await mockApiService.createOrder(additionalTestOrders[0]);
      this.log(`MockAPI service created order ${newOrder.id}`, 'success');
      
      // Test updateOrder
      const updateData = {
        status: 'shipped',
        trackingNumber: 'TEST123456789',
        updatedAt: new Date().toISOString()
      };
      
      const updatedOrder = await mockApiService.updateOrder(newOrder.id, updateData);
      this.log(`MockAPI service updated order ${updatedOrder.id} status to ${updatedOrder.status}`, 'success');
      
      return { orders, newOrder, updatedOrder };
      
    } catch (error) {
      this.log(`MockAPI service test failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async testLocalAPIEndpoints() {
    this.log('Testing local API endpoints...', 'test');
    
    try {
      // Test GET /api/orders
      const response = await fetch(`${LOCAL_SERVER}/api/orders`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      this.log(`Local API returned ${result.data ? result.data.length : 'unknown'} orders`, 'success');
      
      // Test POST /api/orders
      const createResponse = await fetch(`${LOCAL_SERVER}/api/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(additionalTestOrders[1])
      });
      
      if (createResponse.ok) {
        const createResult = await createResponse.json();
        this.log(`Local API created order successfully`, 'success');
        return createResult;
      } else {
        this.log(`Local API create failed: HTTP ${createResponse.status}`, 'warning');
      }
      
    } catch (error) {
      this.log(`Local API test failed: ${error.message}`, 'error');
      // Don't throw - local server might not be running
    }
  }

  async populateTestData() {
    this.log('Populating MockAPI with additional test data...', 'test');
    
    try {
      for (const [index, order] of additionalTestOrders.entries()) {
        const response = await fetch(`${MOCKAPI_URL}/orders`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(order)
        });
        
        if (response.ok) {
          const createdOrder = await response.json();
          this.log(`Created test order ${index + 1}: ${createdOrder.customerName} - $${createdOrder.totalPrice}`, 'success');
        } else {
          this.log(`Failed to create test order ${index + 1}`, 'warning');
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
    } catch (error) {
      this.log(`Test data population failed: ${error.message}`, 'error');
    }
  }

  async runAllTests() {
    this.log('🚀 Starting comprehensive MockAPI order testing...', 'info');
    
    try {
      // Test 1: Direct MockAPI connection
      const directResult = await this.testDirectMockAPI();
      
      // Test 2: MockAPI service functions
      const serviceResult = await this.testMockAPIService();
      
      // Test 3: Local API endpoints
      await this.testLocalAPIEndpoints();
      
      // Test 4: Populate additional test data
      await this.populateTestData();
      
      this.log('🎉 All MockAPI tests completed successfully!', 'success');
      
      return {
        directResult,
        serviceResult,
        testOrdersCreated: additionalTestOrders.length + 1
      };
      
    } catch (error) {
      this.log(`Test suite failed: ${error.message}`, 'error');
      throw error;
    }
  }
}

// Export for use in other scripts
export { MockAPIOrderTester, realWorldOrder, additionalTestOrders };

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new MockAPIOrderTester();
  tester.runAllTests()
    .then(results => {
      console.log('\n📊 Test Results Summary:');
      console.log(`✅ Tests completed successfully`);
      console.log(`📦 Test orders created: ${results.testOrdersCreated}`);
      console.log(`🔗 MockAPI URL: ${MOCKAPI_URL}/orders`);
    })
    .catch(error => {
      console.error('\n❌ Test suite failed:', error.message);
      process.exit(1);
    });
}
