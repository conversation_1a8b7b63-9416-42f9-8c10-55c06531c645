// Comprehensive Status Display Test
class StatusComprehensiveTest {
  constructor() {
    this.testResults = [];
    this.mockStatuses = [
      'pending', 'processing', 'shipped', 'delivered', 
      'completed', 'cancelled', 'refunded', 'on_hold'
    ];
  }

  async runComprehensiveTest() {
    console.log('🧪 Starting Comprehensive Status Display Test...');
    
    // Test 1: Verify current status display
    await this.testCurrentStatusDisplay();
    
    // Test 2: Test all status variations
    this.testAllStatusVariations();
    
    // Test 3: Test status update functionality
    await this.testStatusUpdateFunctionality();
    
    // Test 4: Test responsive design
    this.testResponsiveStatusDisplay();
    
    // Test 5: Test accessibility
    this.testStatusAccessibility();
    
    // Display results
    this.displayTestResults();
    
    // Generate status demo
    this.generateStatusDemo();
  }

  async testCurrentStatusDisplay() {
    console.log('📊 Testing current status display...');
    
    try {
      // Check if orders are loaded
      const orderRows = document.querySelectorAll('#ordersTableBody tr[data-order-id]');
      if (orderRows.length === 0) {
        this.addResult('⚠️', 'No orders found in table - waiting for data...');
        
        // Wait for orders to load
        await new Promise(resolve => setTimeout(resolve, 2000));
        const retryRows = document.querySelectorAll('#ordersTableBody tr[data-order-id]');
        
        if (retryRows.length === 0) {
          this.addResult('❌', 'No orders loaded after waiting');
          return;
        }
      }

      // Check status column position
      const headers = Array.from(document.querySelectorAll('thead th'));
      const statusIndex = headers.findIndex(th => th.textContent.includes('Status'));
      const amountIndex = headers.findIndex(th => th.textContent.includes('Amount'));
      const dateIndex = headers.findIndex(th => th.textContent.includes('Date'));

      if (statusIndex > amountIndex && statusIndex < dateIndex) {
        this.addResult('✅', 'Status column correctly positioned between Amount and Date');
      } else {
        this.addResult('❌', `Status column position incorrect: ${statusIndex} (should be between ${amountIndex} and ${dateIndex})`);
      }

      // Check status badges
      const statusBadges = document.querySelectorAll('#ordersTableBody .badge');
      if (statusBadges.length > 0) {
        this.addResult('✅', `Found ${statusBadges.length} status badges`);
        
        // Check badge styling
        let properlyStyled = 0;
        statusBadges.forEach(badge => {
          if (badge.classList.contains('badge') && 
              Array.from(badge.classList).some(cls => cls.startsWith('bg-'))) {
            properlyStyled++;
          }
        });
        
        this.addResult('✅', `${properlyStyled}/${statusBadges.length} badges properly styled`);
      } else {
        this.addResult('❌', 'No status badges found');
      }

    } catch (error) {
      this.addResult('❌', `Error testing current display: ${error.message}`);
    }
  }

  testAllStatusVariations() {
    console.log('🎨 Testing all status variations...');
    
    // Create test container
    const testContainer = document.createElement('div');
    testContainer.id = 'status-test-container';
    testContainer.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: white;
      border: 2px solid #007bff;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 9999;
      max-width: 300px;
    `;
    
    testContainer.innerHTML = `
      <h6 style="margin: 0 0 10px 0; color: #007bff;">
        <i class="fas fa-vial me-2"></i>Status Badge Test
      </h6>
      <div id="status-badges-demo"></div>
      <button onclick="document.getElementById('status-test-container').remove()" 
              style="margin-top: 10px; padding: 5px 10px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
        Close
      </button>
    `;
    
    document.body.appendChild(testContainer);
    
    const demoContainer = document.getElementById('status-badges-demo');
    
    // Test each status
    this.mockStatuses.forEach(status => {
      const badge = this.createStatusBadge(status);
      const wrapper = document.createElement('div');
      wrapper.style.marginBottom = '5px';
      wrapper.appendChild(badge);
      demoContainer.appendChild(wrapper);
    });
    
    this.addResult('✅', `Generated demo for ${this.mockStatuses.length} status types`);
  }

  createStatusBadge(status) {
    // Use the same logic as the OrderManager
    const statusClass = this.getStatusClass(status);
    const statusIcon = this.getStatusIcon(status);
    const statusText = this.formatStatusText(status);
    
    const badge = document.createElement('span');
    badge.className = `badge bg-${statusClass} text-white status-badge`;
    badge.innerHTML = `<i class="fas fa-${statusIcon} me-1"></i>${statusText}`;
    badge.title = `Status: ${statusText}`;
    
    return badge;
  }

  getStatusClass(status) {
    const normalizedStatus = (status || '').toString().toLowerCase().trim();
    const statusMap = {
      'pending': 'warning',
      'processing': 'info',
      'shipped': 'primary',
      'delivered': 'success',
      'completed': 'success',
      'cancelled': 'danger',
      'refunded': 'secondary',
      'on_hold': 'warning'
    };
    return statusMap[normalizedStatus] || 'secondary';
  }

  getStatusIcon(status) {
    const normalizedStatus = (status || '').toString().toLowerCase().trim();
    const iconMap = {
      'pending': 'clock',
      'processing': 'cog fa-spin',
      'shipped': 'truck',
      'delivered': 'check-circle',
      'completed': 'check-double',
      'cancelled': 'times-circle',
      'refunded': 'undo',
      'on_hold': 'pause-circle'
    };
    return iconMap[normalizedStatus] || 'question-circle';
  }

  formatStatusText(status) {
    if (!status) return 'Unknown';
    const statusStr = status.toString().trim();
    if (statusStr.toLowerCase() === 'on_hold') return 'On Hold';
    return statusStr.charAt(0).toUpperCase() + statusStr.slice(1).toLowerCase();
  }

  async testStatusUpdateFunctionality() {
    console.log('⚙️ Testing status update functionality...');
    
    // Check if status update modal exists
    const modal = document.getElementById('statusUpdateModal');
    if (modal) {
      this.addResult('✅', 'Status update modal found');
      
      // Check form elements
      const elements = ['statusOrderId', 'currentStatus', 'newStatus', 'updateStatusBtn'];
      elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          this.addResult('✅', `Modal element ${id} found`);
        } else {
          this.addResult('❌', `Modal element ${id} missing`);
        }
      });
    } else {
      this.addResult('❌', 'Status update modal not found');
    }
  }

  testResponsiveStatusDisplay() {
    console.log('📱 Testing responsive status display...');
    
    // Test different viewport sizes
    const originalWidth = window.innerWidth;
    
    // Mobile test
    Object.defineProperty(window, 'innerWidth', { value: 480, configurable: true });
    window.dispatchEvent(new Event('resize'));
    
    setTimeout(() => {
      const badges = document.querySelectorAll('.status-badge');
      if (badges.length > 0) {
        this.addResult('✅', 'Status badges responsive to mobile viewport');
      }
      
      // Restore original width
      Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  testStatusAccessibility() {
    console.log('♿ Testing status accessibility...');
    
    const badges = document.querySelectorAll('.status-badge');
    let accessibleBadges = 0;
    
    badges.forEach(badge => {
      // Check for title attribute (tooltip)
      if (badge.hasAttribute('title')) {
        accessibleBadges++;
      }
      
      // Check for proper contrast (basic check)
      const computedStyle = window.getComputedStyle(badge);
      const bgColor = computedStyle.backgroundColor;
      const textColor = computedStyle.color;
      
      if (bgColor && textColor) {
        // Basic contrast check passed
      }
    });
    
    if (accessibleBadges === badges.length) {
      this.addResult('✅', 'All status badges have accessibility features');
    } else {
      this.addResult('⚠️', `${accessibleBadges}/${badges.length} badges have accessibility features`);
    }
  }

  generateStatusDemo() {
    console.log('🎭 Generating interactive status demo...');
    
    // Add demo button to page
    const demoButton = document.createElement('button');
    demoButton.innerHTML = '<i class="fas fa-palette me-2"></i>Status Demo';
    demoButton.className = 'btn btn-info btn-sm';
    demoButton.style.cssText = 'position: fixed; bottom: 20px; right: 20px; z-index: 9999;';
    demoButton.onclick = () => this.showStatusDemo();
    
    document.body.appendChild(demoButton);
    
    this.addResult('✅', 'Interactive status demo button added');
  }

  showStatusDemo() {
    // Create comprehensive status demo modal
    const demoModal = document.createElement('div');
    demoModal.innerHTML = `
      <div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-palette me-2"></i>Status Badge Showcase
              </h5>
              <button type="button" class="btn-close" onclick="this.closest('.modal').parentElement.remove()"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                ${this.mockStatuses.map(status => `
                  <div class="col-md-6 mb-3">
                    <div class="d-flex align-items-center justify-content-between p-2 border rounded">
                      <span class="fw-semibold">${this.formatStatusText(status)}:</span>
                      ${this.createStatusBadge(status).outerHTML}
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').parentElement.remove()">Close</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(demoModal);
  }

  addResult(status, message) {
    this.testResults.push({ status, message, timestamp: Date.now() });
    console.log(`${status} ${message}`);
  }

  displayTestResults() {
    console.log('\n🧪 COMPREHENSIVE STATUS TEST RESULTS');
    console.log('====================================');
    
    const passed = this.testResults.filter(r => r.status === '✅').length;
    const failed = this.testResults.filter(r => r.status === '❌').length;
    const warnings = this.testResults.filter(r => r.status === '⚠️').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`📊 Success Rate: ${Math.round((passed / this.testResults.length) * 100)}%`);
    
    // Show notification
    if (window.notifications) {
      const successRate = Math.round((passed / this.testResults.length) * 100);
      window.notifications.showToast(
        `Status display test complete: ${successRate}% success rate`,
        successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error',
        'Status Test Complete'
      );
    }
  }
}

// Auto-run test when page loads (if in test mode)
if (window.location.search.includes('status-comprehensive=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const test = new StatusComprehensiveTest();
      test.runComprehensiveTest();
    }, 3000);
  });
}

// Make test available globally
window.StatusComprehensiveTest = StatusComprehensiveTest;

console.log('🧪 Status Comprehensive Test loaded. Run with: new StatusComprehensiveTest().runComprehensiveTest();');
