// controllers/productTestingController.js

import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SIMULATIONS_FILE_PATH = path.join(__dirname, '../data/productTests.json');

// Helper to load product simulations/tests
async function loadProductSimulations() {
  try {
    const data = await fs.readFile(SIMULATIONS_FILE_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (e) {
    if (e.code === 'ENOENT') {
      return []; // File not found, return empty array
    }
    console.error('Error loading product simulations:', e);
    throw e; // Re-throw other errors
  }
}

// Helper to save product simulations
async function saveProductSimulations(simulations) {
  try {
    await fs.writeFile(SIMULATIONS_FILE_PATH, JSON.stringify(simulations, null, 2));
  } catch (e) {
    console.error('Error saving product simulations:', e);
    throw e;
  }
}

// Helper to load products (for dropdown in modal)
async function loadProducts() {
  try {
    const inventoryPath = path.join(__dirname, '../data/inventory.json');
    const data = await fs.readFile(inventoryPath, 'utf8');
    const inventory = JSON.parse(data);
    return inventory.filter(item => item.type === 'product'); // Assuming finished goods are type 'product'
  } catch (error) {
    console.warn('Could not load products for simulation form:', error.message);
    return [];
  }
}

export const showProductTesting = async (req, res) => {
  try {
    const productSimulations = await loadProductSimulations();
    const products = await loadProducts(); // Load products for the modal
    res.render('product-testing', {
      title: 'Product Testing & Simulations',
      currentRoute: '/product-testing',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/productTesting.js', '/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      excludeHeader: false,
      excludeFooter: false,
      productSimulations,
      products,
      error: null
    });
  } catch (error) {
    console.error('Error loading product simulations page:', error);
    res.status(500).render('error', {
        pageTitle: 'Error',
        message: 'Failed to load product simulations page.',
        error,
        currentRoute: '/product-testing'
    });
  }
};

// API: Get all simulations
export const getAllSimulations = async (req, res) => {
  try {
    const simulations = await loadProductSimulations();
    res.json(simulations);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching simulations', error: error.message });
  }
};

// API: Get a single simulation by ID
export const getSimulationById = async (req, res) => {
  try {
    const simulations = await loadProductSimulations();
    const simulation = simulations.find(s => s.id === req.params.id);
    if (simulation) {
      res.json(simulation);
    } else {
      res.status(404).json({ message: 'Simulation not found' });
    }
  } catch (error) {
    res.status(500).json({ message: 'Error fetching simulation', error: error.message });
  }
};

// API: Create a new simulation
export const createSimulation = async (req, res) => {
  try {
    const simulations = await loadProductSimulations();
    const newSimulation = {
      id: Date.now().toString(), // Simple ID
      ...req.body,
      createdAt: new Date().toISOString(),
      lastRun: null,
      status: req.body.status || 'Pending',
      result: req.body.result || 'Not yet run'
    };
    simulations.push(newSimulation);
    await saveProductSimulations(simulations);
    res.status(201).json(newSimulation);
  } catch (error) {
    res.status(500).json({ message: 'Error creating simulation', error: error.message });
  }
};

// API: Update a simulation
export const updateSimulation = async (req, res) => {
  try {
    const simulations = await loadProductSimulations();
    const index = simulations.findIndex(s => s.id === req.params.id);
    if (index > -1) {
      simulations[index] = { ...simulations[index], ...req.body, updatedAt: new Date().toISOString() };
      await saveProductSimulations(simulations);
      res.json(simulations[index]);
    } else {
      res.status(404).json({ message: 'Simulation not found' });
    }
  } catch (error) {
    res.status(500).json({ message: 'Error updating simulation', error: error.message });
  }
};

// API: Delete a simulation
export const deleteSimulation = async (req, res) => {
  try {
    let simulations = await loadProductSimulations();
    const initialLength = simulations.length;
    simulations = simulations.filter(s => s.id !== req.params.id);
    if (simulations.length < initialLength) {
      await saveProductSimulations(simulations);
      res.status(200).json({ message: 'Simulation deleted successfully' });
    } else {
      res.status(404).json({ message: 'Simulation not found' });
    }
  } catch (error) {
    res.status(500).json({ message: 'Error deleting simulation', error: error.message });
  }
};

// API: Run a simulation (placeholder - actual simulation logic would be complex)
export const runSimulation = async (req, res) => {
  try {
    const simulations = await loadProductSimulations();
    const index = simulations.findIndex(s => s.id === req.params.id);
    if (index > -1) {
      // Simulate a run
      simulations[index].status = 'Running';
      await saveProductSimulations(simulations);
      // Simulate some processing time
      setTimeout(async () => {
        const currentSims = await loadProductSimulations(); // Re-load to avoid race conditions if multiple runs
        const currentIdx = currentSims.findIndex(s => s.id === req.params.id);
        if (currentIdx > -1) {
            currentSims[currentIdx].status = Math.random() > 0.3 ? 'Completed' : 'Failed';
            currentSims[currentIdx].lastRun = new Date().toISOString();
            currentSims[currentIdx].result = currentSims[currentIdx].status === 'Completed' ? 'Simulation successful, all parameters within tolerance.' : 'Simulation failed: critical parameter out of bounds.';
            currentSims[currentIdx].logs = [`[${new Date().toISOString()}] Simulation started.`, `[${new Date().toISOString()}] Processing scenario: ${currentSims[currentIdx].scenario}`, `[${new Date().toISOString()}] Simulation finished with status: ${currentSims[currentIdx].status}`];
            await saveProductSimulations(currentSims);
            res.json(currentSims[currentIdx]);
        } else {
            // Should not happen if ID is valid
            res.status(404).json({ message: 'Simulation disappeared during run' });
        }
      }, 2000);
    } else {
      res.status(404).json({ message: 'Simulation not found' });
    }
  } catch (error) {
    console.error("Error running simulation:", error);
    // Attempt to revert status if it was set to Running
    const simulations = await loadProductSimulations();
    const index = simulations.findIndex(s => s.id === req.params.id);
    if (index > -1 && simulations[index].status === 'Running') {
        simulations[index].status = 'Failed'; // Or revert to previous status
        simulations[index].result = 'Execution error during simulation run.';
        await saveProductSimulations(simulations);
    }
    res.status(500).json({ message: 'Error running simulation', error: error.message });
  }
};