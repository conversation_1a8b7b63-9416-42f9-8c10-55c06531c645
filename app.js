import express from 'express';
import path from 'path';
import http from 'http';
import cors from 'cors';
import { setupWebSocketServer } from './websocket.js';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

import expressLayouts from 'express-ejs-layouts';
// import createError from 'http-errors'; // Removed unused import
import dotenv from 'dotenv';

import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { showDashboard } from './controllers/dashboardController.js';
import dataService from './services/dataService.js';
import apiOrdersRoutes from './routes/api/orders.js';
import inventoryViewRoutes from './routes/inventoryViewRoutes.js'; // Added for inventory page
import inventoryApiRoutes from './routes/inventoryApiRoutes.js'; // Added for inventory API
import dashboardService from './services/dashboardService.js'; // Ensure dashboardService is imported
import multer from 'multer'; // Import multer
import xlsx from 'xlsx'; // Import xlsx
import mockapiRoutes from './routes/mockapi.js'; // <-- Add this import
import eventsApiRoutes from './routes/api/events.js'; // Events API routes

import aiHelperRoutes from './routes/ai-helper.js';
import apiTestRoutes from './routes/apiTestRoutes.js';
import eventsRoutes from './routes/eventsRoutes.js';
import ordersRoutes from './routes/ordersRoutes.js';
import customerRoutes from './routes/customerRoutes.js';
import productTestingRoutes from './routes/productTestingRoutes.js';
import accountingRoutes from './routes/accountingRoutes.js';
import orderManagementRoutes from './routes/orderManagementRoutes.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const server = http.createServer(app);

// Multer setup for file uploads (memory storage for now)
const storage = multer.memoryStorage(); // Stores file in memory
const upload = multer({ storage: storage });

// Initialize app configuration
function initializeApp() {
  // --- Best Practice: Modular Middleware Setup ---
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Security: Helmet, CORS, Rate Limiting
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net", "https://sdk.canva.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"],
        fontSrc: ["'self'", "https://cdnjs.cloudflare.com", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https://*.canva.com", "https://export-download.canva.com"],
        connectSrc: ["'self'", process.env.NODE_ENV === 'development' ? "ws://localhost:*" : "wss://*", "https://api.canva.com", "https://www.canva.com", "https://*.picard.replit.dev"],
        frameSrc: ["'self'", "https://www.canva.com", "https://*.canva.com", "https://www.canva-hosted-embed.com"]
      }
    }
  }));

  // Enhanced CORS configuration for production and development
  const allowedOrigins = [
    // Development origins
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:10000',
    'http://127.0.0.1:10000',

    // Production origins - Updated with correct Render URL
    'https://shop-easly-admin.onrender.com',
    'https://www.shop-easly-admin.onrender.com',
    'https://shopeasly.onrender.com',
    'https://www.shopeasly.onrender.com',

    // Additional development origins
    'http://localhost:8080',
    'http://localhost:5000'
  ];

  // Add environment-specific origins if they exist
  if (process.env.PRODUCTION_ORIGIN) {
    allowedOrigins.push(process.env.PRODUCTION_ORIGIN);
  }
  if (process.env.FRONTEND_URL) {
    allowedOrigins.push(process.env.FRONTEND_URL);
  }

  console.log('🔧 CORS Configuration:');
  console.log('📋 Allowed Origins:', allowedOrigins);
  console.log('🌍 NODE_ENV:', process.env.NODE_ENV);
  console.log('🔗 PRODUCTION_ORIGIN:', process.env.PRODUCTION_ORIGIN);
  console.log('🔗 FRONTEND_URL:', process.env.FRONTEND_URL);

  app.use(cors({
    origin: function (origin, callback) {
      console.log('🔍 CORS Check - Origin:', origin);

      // Allow requests with no origin (like mobile apps, curl, Postman, server-to-server)
      if (!origin) {
        console.log('✅ CORS: Allowing request with no origin');
        return callback(null, true);
      }

      // Check if origin is in allowed list
      if (allowedOrigins.includes(origin)) {
        console.log('✅ CORS: Origin allowed:', origin);
        return callback(null, true);
      }

      // In development, be more permissive
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 CORS: Development mode - allowing origin:', origin);
        return callback(null, true);
      }

      // Production: Check for Render.com domains and common patterns
      if (origin && (
        origin.includes('.onrender.com') ||
        origin.includes('shopeasly') ||
        origin.includes('shop-easly')
      )) {
        console.log('✅ CORS: Allowing Render.com/ShopEasly domain:', origin);
        return callback(null, true);
      }

      // Additional safety check for localhost in any environment
      if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
        console.log('✅ CORS: Allowing localhost origin:', origin);
        return callback(null, true);
      }

      console.log('❌ CORS: Origin not allowed:', origin);
      console.log('📋 Allowed origins:', allowedOrigins);
      console.log('🌍 Current NODE_ENV:', process.env.NODE_ENV);
      return callback(new Error(`CORS: Origin ${origin} not allowed`));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
    exposedHeaders: ['Content-Range', 'X-Content-Range'],
    maxAge: 86400 // 24 hours
  }));

  app.use(rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again after 15 minutes'
  }));



  // --- Best Practice: Static Files & View Engine ---
  app.use(express.static(path.join(__dirname, 'public')));
  app.set('views', path.join(__dirname, 'views'));
  app.set('view engine', 'ejs');
  app.use(expressLayouts);
  app.set('layout', 'layout');
  app.set('layout extractScripts', true);
  app.set('layout extractStyles', true);

  // --- Best Practice: Template Variables Middleware ---
  app.use((req, res, next) => {
    res.locals.title = res.locals.title || 'ShopEasly';
    res.locals.currentRoute = req.path;
    next();
  });

  // Route: index (landing page)
  app.get('/', async (_req, res) => { // Make the route async
    try {
      // Fetch data using dashboardService
      const dashboardData = await dashboardService.getDashboardData();
      res.render('index', {
        layout: false,  // Completely bypass layout system
        title: 'Shop Easly',
        excludeSidebar: true,
        excludeHeader: true,
        excludeFooter: true,
        pageStyles: '/css/index.css',
        pageScripts: '/js/index.js',
        // Pass the dynamic data to the template
        totalRevenue: dashboardData.revenue || 0,
        totalOrders: dashboardData.orders || 0,
        // You can add more data points here if needed, e.g., products managed
        productsManaged: dashboardData.inventoryStock || 0
      });
    } catch (error) {
      console.error("Error fetching data for index page:", error);
      // Fallback rendering in case of an error
      res.render('index', {
        layout: false,
        title: 'Shop Easly',
        excludeSidebar: true,
        excludeHeader: true,
        excludeFooter: true,
        pageStyles: '/css/index.css',
        pageScripts: '/js/index.js',
        totalRevenue: 'N/A', // Indicate data is not available
        totalOrders: 'N/A',
        productsManaged: 'N/A'
      });
    }
  });

  app.get('/dashboard', showDashboard);



  // --- Best Practice: Modular Route Mounting ---
  app.use('/inventory', inventoryViewRoutes);
  app.use('/api/orders', apiOrdersRoutes);
  app.use('/api/inventory', inventoryApiRoutes);
  app.use('/api/events', eventsApiRoutes);
  app.use('/mockapi', mockapiRoutes);
  app.use('/ai-helper', aiHelperRoutes);
  app.use('/events', eventsRoutes);
  app.use('/orders', ordersRoutes);
  app.use('/customers', customerRoutes);
  app.use('/product-testing', productTestingRoutes);
  app.use('/accounting', accountingRoutes); // Use accountingRoutes as the router
  app.use('/order-management-demo', orderManagementRoutes);
  app.use('/api-test', apiTestRoutes);

  // API route to add a new product
  app.post('/api/inventory/products', async (req, res) => {
    try {
      const newProduct = await dataService.createProduct(req.body);
      res.status(201).json({ message: 'Product added successfully', product: newProduct });
    } catch (error) {
      console.error('Error adding product:', error);
      res.status(500).json({ error: 'Failed to add product' });
    }
  });

  // API route to add a new material
  app.post('/api/inventory/materials', async (req, res) => {
    try {
      const newMaterial = await dataService.createMaterial(req.body);
      // This is a simulation, actual persistence depends on createMaterial implementation
      res.status(201).json({ message: 'Material added successfully (simulated)', material: newMaterial });
    } catch (error) {
      console.error('Error adding material:', error);
      res.status(500).json({ error: 'Failed to add material' });
    }
  });

  // API route to add a new packaging material
  app.post('/api/inventory/packaging', async (req, res) => {
    try {
      const newPackagingMaterial = await dataService.createPackagingMaterial(req.body);
      res.status(201).json(newPackagingMaterial);
    } catch (error) {
      console.error('Error creating packaging material:', error);
      res.status(500).json({ message: 'Failed to create packaging material', error: error.message });
    }
  });

  // API route to update a product
  app.put('/api/inventory/products/:id', async (req, res) => {
    try {
      const updatedProduct = await dataService.updateProduct(req.params.id, req.body);
      res.json({ message: 'Product updated successfully', product: updatedProduct });
    } catch (error) {
      console.error(`Error updating product ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to update product' });
    }
  });

  // API route to update a material
  app.put('/api/inventory/materials/:id', async (req, res) => {
    try {
      const updatedMaterial = await dataService.updateMaterial(req.params.id, req.body);
      res.json({ message: 'Material updated successfully (simulated)', material: updatedMaterial });
    } catch (error) {
      console.error(`Error updating material ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to update material' });
    }
  });

  // API route to update a packaging material by ID
  app.put('/api/inventory/packaging/:id', async (req, res) => {
    try {
      const updatedPackagingMaterial = await dataService.updatePackagingMaterial(req.params.id, req.body);
      if (!updatedPackagingMaterial) {
        return res.status(404).json({ message: 'Packaging material not found' });
      }
      res.json(updatedPackagingMaterial);
    } catch (error) {
      console.error('Error updating packaging material:', error);
      res.status(500).json({ message: 'Failed to update packaging material', error: error.message });
    }
  });

  // API route to delete a product
  app.delete('/api/inventory/products/:id', async (req, res) => {
    try {
      await dataService.deleteProduct(req.params.id);
      res.json({ message: 'Product deleted successfully' });
    } catch (error) {
      console.error(`Error deleting product ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to delete product' });
    }
  });

  // API route to delete a material
  app.delete('/api/inventory/materials/:id', async (req, res) => {
    try {
      await dataService.deleteMaterial(req.params.id);
      res.json({ message: 'Material deleted successfully (simulated)' });
    } catch (error) {
      console.error(`Error deleting material ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to delete material' });
    }
  });

  // API route to delete a packaging material by ID
  app.delete('/api/inventory/packaging/:id', async (req, res) => {
    try {
      const result = await dataService.deletePackagingMaterial(req.params.id);
      if (!result) { // Or check based on how deletePackagingMaterial indicates not found
        return res.status(404).json({ message: 'Packaging material not found' });
      }
      res.status(200).json({ message: 'Packaging material deleted successfully' });
    } catch (error) {
      console.error('Error deleting packaging material:', error);
      res.status(500).json({ message: 'Failed to delete packaging material', error: error.message });
    }
  });



  app.get('/api/inventory', async (_req, res) => {
    try {
      const inventory = await dataService.getInventory();
      res.json(inventory);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch inventory data' });
    }
  });

  app.get('/api/customers', async (_req, res) => {
    try {
      const customers = await dataService.getCustomers();
      res.json(customers);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch customer data' });
    }
  });

  // Add basic routes for main pages
  app.get('/orders', async (_req, res) => {
    try {
      const orders = await dataService.getOrders();
      res.render('orders', {
        title: 'Orders',
        currentRoute: '/orders',
        pageStyles: ['/css/sidebar.css'],
        pageScripts: ['/js/sidebar.js'],
        excludeParticles: true,
        excludeSidebar: false,
        orders
      });
    } catch (error) {
      console.error('Error loading orders:', error);
      res.status(500).send('Error loading orders');
    }
  });

  app.get('/customers', async (_req, res) => {
    try {
      const customers = await dataService.getCustomers();
      res.render('customers', {
        title: 'Customers',
        currentRoute: '/customers',
        pageStyles: ['/css/sidebar.css'],
        pageScripts: ['/js/sidebar.js'],
        excludeParticles: true,
        excludeSidebar: false,
        customers
      });
    } catch (error) {
      console.error('Error loading customers:', error);
      res.status(500).send('Error loading customers');
    }
  });
  app.get('/events', (_req, res) => {
    res.render('events', {
      title: 'Events & Design Studio',
      currentRoute: '/events',
      pageStyles: ['/css/sidebar.css', '/css/events.css'],
      pageScripts: ['/js/sidebar.js', '/js/events-enhanced.js', '/js/canva-integration.js'],
      excludeParticles: true,
      excludeSidebar: false,
      events: []
    });
  });

  app.get('/product-testing', (_req, res) => {
    res.render('productTesting', {
      title: 'Product Testing',
      currentRoute: '/product-testing',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      tests: []
    });
  });

  app.get('/accounting', (_req, res) => {
    res.render('accounting', {
      title: 'Financial Intelligence Dashboard',
      currentRoute: '/accounting',
      pageStyles: ['/css/sidebar.css', '/css/accounting.css'],
      pageScripts: ['/js/accounting.js', '/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      records: []
    });
  });

  app.get('/ai-helper', (_req, res) => {
    res.render('aiHelper', {
      title: 'AI Helper',
      currentRoute: '/ai-helper',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false
    });
  });

  // Promotional Signup Routes
  app.get('/signup', (_req, res) => {
    res.render('signup', {
      title: 'Join Our Promotions - ShopEasly',
      error: null,
      success: null,
      formData: {}
    });
  });

  app.post('/signup', async (req, res) => {
    try {
      const { name, email, phone, address, emailMarketing, smsMarketing } = req.body;

      // Validation
      const errors = [];

      // Name is required
      if (!name || name.trim().length < 2) {
        errors.push('Name is required and must be at least 2 characters');
      }

      // At least one contact method required
      const hasEmail = email && email.trim();
      const hasPhone = phone && phone.trim();

      if (!hasEmail && !hasPhone) {
        errors.push('Please provide either an email address or phone number');
      }

      // Validate email format if provided
      if (hasEmail) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.trim())) {
          errors.push('Please provide a valid email address');
        }
      }

      // Validate phone format if provided
      if (hasPhone) {
        const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
        if (cleanPhone.length < 10) {
          errors.push('Please provide a valid phone number');
        }
      }

      if (errors.length > 0) {
        if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
          return res.status(400).json({
            success: false,
            error: errors.join(', ')
          });
        } else {
          return res.render('signup', {
            title: 'Join Our Promotions - ShopEasly',
            error: errors.join(', '),
            success: null,
            formData: req.body
          });
        }
      }

      // Create customer data object
      const customerData = {
        name: name.trim(),
        email: email ? email.trim() : null,
        phone: phone ? phone.trim() : null,
        address: address ? address.trim() : null,
        source: 'promotional_signup',
        marketingPreferences: {
          email: emailMarketing === 'true' || emailMarketing === true,
          sms: smsMarketing === 'true' || smsMarketing === true
        },
        joinedAt: new Date().toISOString(),
        isPromoCustomer: true
      };

      // Save customer to data file
      const customerController = await import('./controllers/customerController.js');
      const result = await customerController.addPromoCustomer(customerData);

      if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
        res.json({
          success: true,
          message: 'Successfully joined promotions!',
          customerId: result.id
        });
      } else {
        res.render('signup', {
          title: 'Join Our Promotions - ShopEasly',
          error: null,
          success: true,
          formData: {}
        });
      }

    } catch (error) {
      console.error('Signup error:', error);

      if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
        res.status(500).json({
          success: false,
          error: 'Something went wrong. Please try again.'
        });
      } else {
        res.render('signup', {
          title: 'Join Our Promotions - ShopEasly',
          error: 'Something went wrong. Please try again.',
          success: null,
          formData: req.body
        });
      }
    }
  });

  // --- Best Practice: Centralized Error Handling ---
  app.use((req, res, _next) => {
    res.status(404);
    if (req.accepts('html')) {
      res.render('404', {
        title: '404 - Page Not Found',
        url: req.url
      });
      return;
    }
    if (req.accepts('json')) {
      res.json({ error: 'Not found' });
      return;
    }
    res.type('txt').send('Not found');
  });

  app.use((err, req, res, _next) => {
    res.locals.message = err.message;
    res.locals.error = process.env.NODE_ENV === 'development' ? err : {};
    res.locals.title = 'Error';
    console.error(err.stack);
    if (req.accepts('html')) {
      res.status(err.status || 500);
      res.render('error');
      return;
    }
    res.status(err.status || 500).json({
      error: {
        message: err.message,
        status: err.status
      }
    });
  });

  // --- Best Practice: Server Startup ---
  setupWebSocketServer(server);
  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
}

// Refactor dynamic imports to use .then() for compatibility
function initializeDynamicRoutes() {
  // Load customer API routes
  import('./routes/api/customers.js').then((module) => {
    const customersApiRoutes = module.default;
    app.use('/api/customers', customersApiRoutes);
    console.log('✅ Customer API routes loaded');
  }).catch((error) => {
    console.error('❌ Error loading Customer API routes:', error);
  });

  import('./routes/api/production.js').then((module) => {
    const productionApiRoutes = module.default;
    app.use('/api/production', productionApiRoutes);
    console.log('✅ Production API routes loaded');
  }).catch((error) => {
    console.error('❌ Error loading production API routes:', error);
    // Don't exit the process, just log the error
    console.error('Application will continue without production API routes');
  });

  app.get('/api/customer-analytics', (_req, res) => {
    import('./services/customerAnalyticsService.js').then((module) => {
      const customerAnalyticsService = module.default;
      return customerAnalyticsService.analyzeCustomerData();
    }).then((analytics) => {
      res.json(analytics);
    }).catch((error) => {
      console.error('Error fetching customer analytics:', error);
      res.status(500).json({ error: 'Failed to fetch customer analytics' });
    });
  });

  app.get('/api/customer-analytics/export', async (_req, res) => {
    try {
      const module = await import('./services/customerAnalyticsService.js');
      const customerAnalyticsService = module.default;
      await customerAnalyticsService.analyzeCustomerData();
      const exportData = await customerAnalyticsService.exportCustomerData();
      res.json(exportData);
    } catch (error) {
      console.error('Error exporting customer data:', error);
      res.status(500).json({ error: 'Failed to export customer data' });
    }
  });
}

// Update initialization to include dynamic routes
initializeApp();
initializeDynamicRoutes();
