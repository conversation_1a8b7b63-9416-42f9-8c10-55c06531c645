/* Order Management Styles */

/* Simple Status Text Styling */
.orders-table td:nth-child(5) {
  text-align: center !important;
  vertical-align: middle !important;
  font-weight: 500 !important;
  text-transform: capitalize !important;
}

/* Timeline Styles */
.timeline-item {
  position: relative;
}

.timeline-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 4px;
}

.timeline-content {
  flex: 1;
}

/* Status Indicator Styles */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* Enhanced Modal Styles */
.modal-lg {
  max-width: 900px;
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 12px 12px;
  background-color: #f8f9fa;
}

/* Card Enhancements */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #495057;
}

/* Tracking Status Colors */
.bg-delivered { background-color: #28a745 !important; }
.bg-out_for_delivery { background-color: #ffc107 !important; }
.bg-in_transit { background-color: #17a2b8 !important; }
.bg-shipped { background-color: #007bff !important; }
.bg-label_created { background-color: #6c757d !important; }

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Customer Profile Enhancements */
.customer-info .mb-2 {
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.customer-info .mb-2:last-child {
  border-bottom: none;
}

.customer-stats .d-flex {
  padding: 0.25rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.customer-stats .d-flex:last-child {
  border-bottom: none;
}

/* Table Enhancements */
.table-sm th,
.table-sm td {
  padding: 0.5rem;
  vertical-align: middle;
}

.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

/* Button Enhancements */
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Dropdown Enhancements */
.dropdown-menu {
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 0.5rem 0;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  transform: translateX(4px);
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

/* Alert Enhancements */
.alert {
  border: none;
  border-radius: 8px;
  font-weight: 500;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0277bd;
}

.alert-warning {
  background-color: #fff3e0;
  color: #ef6c00;
}

.alert-danger {
  background-color: #ffebee;
  color: #c62828;
}

/* Badge Enhancements */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
  border-radius: 6px;
}

/* Form Enhancements */
.form-control,
.form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: all 0.2s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .modal-lg {
    max-width: 95%;
    margin: 1rem auto;
  }

  .modal-body {
    padding: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .timeline-content {
    font-size: 0.875rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}