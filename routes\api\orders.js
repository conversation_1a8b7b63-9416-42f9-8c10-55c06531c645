// API Routes for Order Management
import express from 'express';
import dataService from '../../services/dataService.js';
import InvoiceGenerator from '../../utils/invoiceGenerator.js';
import TrackingService from '../../utils/trackingService.js';
import { body, validationResult } from 'express-validator';

const router = express.Router();
const invoiceGenerator = new InvoiceGenerator();
const trackingService = new TrackingService();

function sanitizeOrderData(orderData) {
  const sanitized = {};

  if (orderData.customerName) sanitized.customerName = orderData.customerName.trim();
  if (orderData.customerEmail) sanitized.customerEmail = orderData.customerEmail.trim().toLowerCase();
  if (orderData.customerAddress) sanitized.customerAddress = orderData.customerAddress.trim();
  if (orderData.customerId) sanitized.customerId = orderData.customerId.trim();
  if (orderData.totalPrice !== undefined) sanitized.totalPrice = parseFloat(orderData.totalPrice);
  if (orderData.status) sanitized.status = orderData.status.toLowerCase();
  if (orderData.items) sanitized.items = orderData.items;

  return sanitized;
}

// GET /api/orders - Get all orders with optional filtering and pagination
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      customerName,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let orders = await dataService.getOrders();

    // Apply filters
    if (status) {
      orders = orders.filter(order => order.status === status.toLowerCase());
    }

    if (customerName) {
      orders = orders.filter(order =>
        order.customerName.toLowerCase().includes(customerName.toLowerCase())
      );
    }

    // Apply sorting
    orders.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'createdAt') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (sortBy === 'totalPrice') {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedOrders = orders.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedOrders,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(orders.length / parseInt(limit)),
        totalItems: orders.length,
        itemsPerPage: parseInt(limit)
      },
      filters: { status, customerName, sortBy, sortOrder }
    });

  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch orders',
      message: error.message
    });
  }
});

// GET /api/orders/:id - Get a specific order by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const orders = await dataService.getOrders();
    const order = orders.find(o => o.id === id);

    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${id}`
      });
    }

    res.json({
      success: true,
      data: order
    });

  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch order',
      message: error.message
    });
  }
});

// POST /api/orders - Create a new order
router.post(
  '/',
  [
    body('customerName').isString().isLength({ min: 2 }).withMessage('Customer name must be at least 2 characters'),
    body('customerEmail').isEmail().withMessage('Valid email address is required'),
    body('customerAddress').isString().isLength({ min: 5 }).withMessage('Customer address must be at least 5 characters'),
    body('totalPrice').isFloat({ min: 0 }).withMessage('Total price must be a valid positive number'),
    body('status').optional().isIn(['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'completed']).withMessage('Invalid status'),
    body('items').isArray().withMessage('Items must be an array'),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      // Sanitize and prepare order data
      const sanitizedData = sanitizeOrderData(req.body);
      // Add default values
      const newOrderData = {
        ...sanitizedData,
        customerId: sanitizedData.customerId || `customer_${Date.now()}`,
        status: sanitizedData.status || 'pending',
        items: sanitizedData.items || [],
        createdAt: new Date().toISOString()
      };
      // Create order via data service
      const newOrder = await dataService.createOrder(newOrderData);
      res.status(201).json({
        success: true,
        data: newOrder,
        message: 'Order created successfully'
      });
    } catch (error) {
      console.error('Error creating order:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create order',
        message: error.message
      });
    }
  }
);

// PUT /api/orders/:id - Update an existing order
router.put(
  '/:id',
  [
    body('customerName').optional().isString().isLength({ min: 2 }).withMessage('Customer name must be at least 2 characters'),
    body('customerEmail').optional().isEmail().withMessage('Valid email address is required'),
    body('customerAddress').optional().isString().isLength({ min: 5 }).withMessage('Customer address must be at least 5 characters'),
    body('totalPrice').optional().isFloat({ min: 0 }).withMessage('Total price must be a valid positive number'),
    body('status').optional().isIn(['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'completed']).withMessage('Invalid status'),
    body('items').optional().isArray().withMessage('Items must be an array'),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    try {
      // Sanitize update data
      const sanitizedData = sanitizeOrderData(req.body);
      // Update order via data service
      const updatedOrder = await dataService.updateOrder(req.params.id, sanitizedData);
      res.json({
        success: true,
        data: updatedOrder,
        message: 'Order updated successfully'
      });
    } catch (error) {
      console.error('Error updating order:', error);
      if (error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          error: 'Order not found',
          message: `No order found with ID: ${req.params.id}`
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to update order',
          message: error.message
        });
      }
    }
  }
);

// PATCH /api/orders/:id/status - Update only the order status
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes, notifyCustomer = true } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Status is required',
        message: 'Please provide a status value'
      });
    }

    const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'completed'];
    if (!validStatuses.includes(status.toLowerCase())) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status',
        message: `Status must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Get the current order to access customer info
    const currentOrder = await dataService.getOrder(id);
    if (!currentOrder) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${id}`
      });
    }

    const oldStatus = currentOrder.status;
    const updateData = {
      status: status.toLowerCase(),
      updatedAt: new Date().toISOString()
    };

    if (notes) {
      updateData.notes = notes;
    }

    const updatedOrder = await dataService.updateOrder(id, updateData);

    // Send customer notification if requested
    if (notifyCustomer && updatedOrder.customerEmail) {
      try {
        await sendCustomerStatusNotification(updatedOrder, status.toLowerCase(), oldStatus);
      } catch (notificationError) {
        console.warn('Failed to send customer notification:', notificationError.message);
        // Don't fail the status update if notification fails
      }
    }

    res.json({
      success: true,
      data: updatedOrder,
      message: `Order status updated to ${status}`,
      customerNotified: notifyCustomer && updatedOrder.customerEmail ? true : false
    });

  } catch (error) {
    console.error('Error updating order status:', error);

    if (error.message.includes('not found')) {
      res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${req.params.id}`
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to update order status',
        message: error.message
      });
    }
  }
});

// Helper function to send customer notifications
async function sendCustomerStatusNotification(order, newStatus, oldStatus) {
  const { sendEmail, sendSMS } = await import('../../utils/notifications.js');

  const statusMessages = {
    'pending': 'Your order has been received and is being processed.',
    'processing': 'Your order is currently being prepared for shipment.',
    'shipped': 'Your order has been shipped and is on its way to you.',
    'delivered': 'Your order has been delivered successfully.',
    'completed': 'Your order has been completed. Thank you for your business!',
    'cancelled': 'Your order has been cancelled. If you have any questions, please contact us.'
  };

  const subject = `Order #${order.id} Status Update`;
  const message = `Hello ${order.customerName || 'Customer'},\n\nYour order status has been updated to: ${newStatus.toUpperCase()}\n\n${statusMessages[newStatus] || 'Your order status has been updated.'}\n\nOrder Details:\n- Order ID: #${order.id}\n- Total: $${order.totalPrice || 'N/A'}\n\nThank you for your business!`;

  // Send email notification
  if (order.customerEmail) {
    await sendEmail(order.customerEmail, subject, message);
  }

  // Send SMS notification if phone number is available
  if (order.customerPhone) {
    const smsMessage = `Order #${order.id} status: ${newStatus.toUpperCase()}. ${statusMessages[newStatus] || 'Status updated.'}`;
    await sendSMS(order.customerPhone, smsMessage);
  }
}

// DELETE /api/orders/:id - Delete an order
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    await dataService.deleteOrder(id);

    res.json({
      success: true,
      message: 'Order deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting order:', error);

    if (error.message.includes('not found')) {
      res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${req.params.id}`
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to delete order',
        message: error.message
      });
    }
  }
});

// POST /api/orders/:id/invoice - Generate and download invoice for an order
router.post('/:id/invoice', async (req, res) => {
  try {
    const { id } = req.params;

    // Get the order
    const order = await dataService.getOrder(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${id}`
      });
    }

    // Generate invoice PDF
    const pdfBuffer = await invoiceGenerator.generateInvoice(order);

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="invoice-${id}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send the PDF
    res.send(pdfBuffer);

  } catch (error) {
    console.error('Error generating invoice:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate invoice',
      message: error.message
    });
  }
});

// GET /api/orders/:id/tracking - Get tracking information for an order
router.get('/:id/tracking', async (req, res) => {
  try {
    const { id } = req.params;

    // Get the order
    const order = await dataService.getOrder(id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found',
        message: `No order found with ID: ${id}`
      });
    }

    // Check if order has tracking number
    if (!order.trackingNumber) {
      return res.status(404).json({
        success: false,
        error: 'No tracking information',
        message: 'This order does not have a tracking number assigned'
      });
    }

    // Get tracking information
    const trackingInfo = await trackingService.getTrackingInfo(
      order.trackingNumber,
      order.carrier || 'fedex'
    );

    res.json({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Error getting tracking info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get tracking information',
      message: error.message
    });
  }
});

export default router;
