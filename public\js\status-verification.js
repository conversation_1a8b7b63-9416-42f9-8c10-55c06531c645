// Status Column Verification and Enhancement Script
class StatusVerification {
  constructor() {
    this.results = [];
    this.statusColorMap = {
      'pending': { color: 'warning', label: 'Pending', icon: 'clock' },
      'processing': { color: 'info', label: 'Processing', icon: 'cog' },
      'shipped': { color: 'primary', label: 'Shipped', icon: 'truck' },
      'delivered': { color: 'success', label: 'Delivered', icon: 'check-circle' },
      'completed': { color: 'success', label: 'Completed', icon: 'check-double' },
      'cancelled': { color: 'danger', label: 'Cancelled', icon: 'times-circle' },
      'refunded': { color: 'secondary', label: 'Refunded', icon: 'undo' },
      'on_hold': { color: 'warning', label: 'On Hold', icon: 'pause-circle' }
    };
  }

  async runVerification() {
    console.log('🔍 Starting Status Column Verification...');
    
    // Test 1: Check if status column exists in table
    this.checkStatusColumnStructure();
    
    // Test 2: Verify status badges are rendered
    this.checkStatusBadgeRendering();
    
    // Test 3: Check status color mapping
    this.checkStatusColorMapping();
    
    // Test 4: Verify status data from API
    await this.checkStatusDataFromAPI();
    
    // Test 5: Test status update functionality
    this.checkStatusUpdateFunctionality();
    
    // Display results and fix issues if found
    this.displayResults();
    this.fixIssuesIfNeeded();
  }

  checkStatusColumnStructure() {
    console.log('🏗️ Checking status column structure...');
    
    // Check table header
    const statusHeader = document.querySelector('th:contains("Status")') || 
                        Array.from(document.querySelectorAll('th')).find(th => th.textContent.includes('Status'));
    
    if (statusHeader) {
      this.addResult('✅', 'Status column header found');
    } else {
      this.addResult('❌', 'Status column header missing');
    }

    // Check if status column is in correct position (between Amount and Date)
    const headers = Array.from(document.querySelectorAll('thead th'));
    const statusIndex = headers.findIndex(th => th.textContent.includes('Status'));
    const amountIndex = headers.findIndex(th => th.textContent.includes('Amount'));
    const dateIndex = headers.findIndex(th => th.textContent.includes('Date'));

    if (statusIndex > amountIndex && statusIndex < dateIndex) {
      this.addResult('✅', 'Status column positioned correctly between Amount and Date');
    } else if (statusIndex !== -1) {
      this.addResult('⚠️', `Status column found but not between Amount and Date (position: ${statusIndex})`);
    } else {
      this.addResult('❌', 'Status column not found in table headers');
    }
  }

  checkStatusBadgeRendering() {
    console.log('🎨 Checking status badge rendering...');
    
    const statusBadges = document.querySelectorAll('#ordersTableBody .badge');
    
    if (statusBadges.length > 0) {
      this.addResult('✅', `Found ${statusBadges.length} status badges`);
      
      // Check if badges have proper Bootstrap classes
      let properlyStyledBadges = 0;
      statusBadges.forEach(badge => {
        if (badge.classList.contains('badge') && 
            (badge.classList.contains('bg-warning') || 
             badge.classList.contains('bg-success') || 
             badge.classList.contains('bg-primary') || 
             badge.classList.contains('bg-info') || 
             badge.classList.contains('bg-danger') || 
             badge.classList.contains('bg-secondary'))) {
          properlyStyledBadges++;
        }
      });
      
      if (properlyStyledBadges === statusBadges.length) {
        this.addResult('✅', 'All status badges have proper Bootstrap styling');
      } else {
        this.addResult('⚠️', `${properlyStyledBadges}/${statusBadges.length} badges properly styled`);
      }
    } else {
      this.addResult('❌', 'No status badges found in table');
    }
  }

  checkStatusColorMapping() {
    console.log('🎨 Checking status color mapping...');
    
    const statusBadges = document.querySelectorAll('#ordersTableBody .badge');
    const statusCounts = {};
    
    statusBadges.forEach(badge => {
      const statusText = badge.textContent.toLowerCase();
      const colorClass = Array.from(badge.classList).find(cls => cls.startsWith('bg-'));
      
      if (!statusCounts[statusText]) {
        statusCounts[statusText] = { count: 0, colorClass: colorClass };
      }
      statusCounts[statusText].count++;
    });

    // Verify color mapping
    Object.keys(statusCounts).forEach(status => {
      const expectedColor = this.statusColorMap[status]?.color;
      const actualColor = statusCounts[status].colorClass?.replace('bg-', '');
      
      if (expectedColor && actualColor === expectedColor) {
        this.addResult('✅', `Status "${status}" has correct color: ${actualColor}`);
      } else if (expectedColor) {
        this.addResult('⚠️', `Status "${status}" color mismatch: expected ${expectedColor}, got ${actualColor}`);
      } else {
        this.addResult('ℹ️', `Status "${status}" not in predefined color map`);
      }
    });
  }

  async checkStatusDataFromAPI() {
    console.log('🌐 Checking status data from API...');
    
    try {
      const response = await fetch('/api/orders?limit=10');
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      const data = await response.json();
      const orders = data.success ? data.data : data;
      
      if (Array.isArray(orders) && orders.length > 0) {
        this.addResult('✅', `API returned ${orders.length} orders`);
        
        // Check status field presence
        const ordersWithStatus = orders.filter(order => order.status);
        const ordersWithoutStatus = orders.length - ordersWithStatus.length;
        
        if (ordersWithoutStatus === 0) {
          this.addResult('✅', 'All orders have status field');
        } else {
          this.addResult('⚠️', `${ordersWithoutStatus} orders missing status field`);
        }
        
        // Check status variety
        const uniqueStatuses = [...new Set(orders.map(order => order.status).filter(Boolean))];
        this.addResult('ℹ️', `Found status types: ${uniqueStatuses.join(', ')}`);
        
      } else {
        this.addResult('❌', 'No orders returned from API');
      }
    } catch (error) {
      this.addResult('❌', `API check failed: ${error.message}`);
    }
  }

  checkStatusUpdateFunctionality() {
    console.log('⚙️ Checking status update functionality...');
    
    // Check if status update modal exists
    const statusModal = document.getElementById('statusUpdateModal');
    if (statusModal) {
      this.addResult('✅', 'Status update modal found');
      
      // Check modal form elements
      const statusSelect = document.getElementById('newStatus');
      if (statusSelect && statusSelect.options.length > 1) {
        this.addResult('✅', `Status select has ${statusSelect.options.length - 1} status options`);
      } else {
        this.addResult('❌', 'Status select not found or empty');
      }
    } else {
      this.addResult('❌', 'Status update modal not found');
    }

    // Check if update status buttons exist
    const updateButtons = document.querySelectorAll('[data-action="update-status"]');
    if (updateButtons.length > 0) {
      this.addResult('✅', `Found ${updateButtons.length} status update buttons`);
    } else {
      this.addResult('❌', 'No status update buttons found');
    }
  }

  addResult(status, message) {
    this.results.push({ status, message, timestamp: Date.now() });
    console.log(`${status} ${message}`);
  }

  displayResults() {
    console.log('\n🔍 STATUS VERIFICATION RESULTS');
    console.log('===============================');
    
    const passed = this.results.filter(r => r.status === '✅').length;
    const failed = this.results.filter(r => r.status === '❌').length;
    const warnings = this.results.filter(r => r.status === '⚠️').length;
    const info = this.results.filter(r => r.status === 'ℹ️').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`ℹ️ Info: ${info}`);
    
    // Show detailed results
    console.log('\nDetailed Results:');
    this.results.forEach(result => {
      console.log(`${result.status} ${result.message}`);
    });

    return { passed, failed, warnings, info, total: this.results.length };
  }

  fixIssuesIfNeeded() {
    const issues = this.results.filter(r => r.status === '❌' || r.status === '⚠️');
    
    if (issues.length > 0) {
      console.log('\n🔧 ATTEMPTING TO FIX ISSUES...');
      
      // Fix missing status badges
      this.enhanceStatusDisplay();
      
      // Fix color mapping
      this.improveStatusColors();
      
      console.log('✅ Status display enhancements applied');
    }
  }

  enhanceStatusDisplay() {
    // Enhance status badges with icons and better styling
    const statusBadges = document.querySelectorAll('#ordersTableBody .badge');
    
    statusBadges.forEach(badge => {
      const statusText = badge.textContent.toLowerCase().trim();
      const statusConfig = this.statusColorMap[statusText];
      
      if (statusConfig) {
        // Add icon if not present
        if (!badge.querySelector('i')) {
          const icon = document.createElement('i');
          icon.className = `fas fa-${statusConfig.icon} me-1`;
          badge.insertBefore(icon, badge.firstChild);
        }
        
        // Ensure proper styling
        badge.className = `badge bg-${statusConfig.color} text-white`;
        badge.textContent = ` ${statusConfig.label}`;
        badge.insertBefore(badge.querySelector('i'), badge.firstChild);
      }
    });
  }

  improveStatusColors() {
    // Add CSS for better status styling
    const style = document.createElement('style');
    style.textContent = `
      .badge.status-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 600;
        text-transform: capitalize;
      }
      
      .badge.status-badge i {
        font-size: 0.7rem;
      }
      
      .status-pending { background-color: #ffc107 !important; color: #000 !important; }
      .status-processing { background-color: #17a2b8 !important; }
      .status-shipped { background-color: #007bff !important; }
      .status-delivered { background-color: #28a745 !important; }
      .status-completed { background-color: #28a745 !important; }
      .status-cancelled { background-color: #dc3545 !important; }
      .status-refunded { background-color: #6c757d !important; }
      .status-on_hold { background-color: #fd7e14 !important; }
    `;
    document.head.appendChild(style);
  }
}

// Auto-run verification when page loads (if in test mode)
if (window.location.search.includes('status-test=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const verification = new StatusVerification();
      verification.runVerification();
    }, 2000);
  });
}

// Make verification available globally
window.StatusVerification = StatusVerification;

console.log('🔍 Status Verification loaded. Run with: new StatusVerification().runVerification();');
