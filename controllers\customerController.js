import path from 'path';
import { promises as fs } from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const customersPath = path.join(__dirname, '../data/customers.json');

async function loadCustomers() {
    try {
        const data = await fs.readFile(customersPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.warn('Customers file missing or invalid. Returning empty array.');
        return [];
    }
}

async function saveCustomers(customers) {
    try {
        await fs.writeFile(customersPath, JSON.stringify(customers, null, 2), 'utf8');
    } catch (error) {
        console.error('Error saving customers:', error.message);
        throw new Error('Failed to save customers');
    }
}

// Generate unique customer ID
function generateCustomerId() {
    return 'CUST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Add promotional customer (flexible validation)
export const addPromoCustomer = async (customerData) => {
    try {
        const customers = await loadCustomers();

        // Check for duplicate email or phone
        const existingCustomer = customers.find(c =>
            (customerData.email && c.email === customerData.email) ||
            (customerData.phone && c.phone === customerData.phone)
        );

        if (existingCustomer) {
            // Update existing customer with new preferences
            existingCustomer.marketingPreferences = customerData.marketingPreferences;
            existingCustomer.lastUpdated = new Date().toISOString();
            if (customerData.address && !existingCustomer.address) {
                existingCustomer.address = customerData.address;
            }
            await saveCustomers(customers);
            return existingCustomer;
        }

        // Create new customer
        const newCustomer = {
            id: generateCustomerId(),
            name: customerData.name,
            email: customerData.email || null,
            phone: customerData.phone || null,
            address: customerData.address || null,
            source: customerData.source || 'promotional_signup',
            marketingPreferences: customerData.marketingPreferences || { email: false, sms: false },
            joinedAt: customerData.joinedAt || new Date().toISOString(),
            isPromoCustomer: true,
            orders: 0,
            totalSpent: 0,
            lastOrderDate: null,
            createdAt: new Date().toISOString()
        };

        customers.push(newCustomer);
        await saveCustomers(customers);

        console.log(`✅ New promotional customer added: ${newCustomer.name} (${newCustomer.id})`);
        return newCustomer;

    } catch (error) {
        console.error('Error adding promotional customer:', error);
        throw new Error('Failed to add customer: ' + error.message);
    }
};

export { loadCustomers };

export const addCustomer = async (req, res) => {
    try {
        const newCustomer = req.body;
        const customers = await loadCustomers();
        customers.push(newCustomer);
        await saveCustomers(customers);
        res.status(201).json(newCustomer);
    } catch (error) {
        console.error('Error adding customer:', error.message);
        res.status(500).json({ error: 'Failed to add customer' });
    }
};

export const updateCustomer = async (req, res) => {
    try {
        const { customerId } = req.params;
        const { customerEmail, customerAddress } = req.body;

        if (!customerEmail || !customerAddress) {
            return res.status(400).json({ error: 'Customer email and address are required' });
        }

        const customers = await loadCustomers();
        const idx = customers.findIndex(c => c.id == customerId);
        if (idx === -1) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        customers[idx].email = customerEmail;
        customers[idx].address = customerAddress;
        await saveCustomers(customers);
        res.json(customers[idx]);
    } catch (error) {
        console.error('Failed to update customer:', error.message);
        res.status(500).json({ error: 'Server error: ' + error.message });
    }
};

export const deleteCustomer = async (req, res) => {
    try {
        const { customerId } = req.params;
        const customers = await loadCustomers();
        const idx = customers.findIndex(c => c.id == customerId);
        if (idx === -1) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        customers.splice(idx, 1);
        await saveCustomers(customers);
        res.json({ success: true });
    } catch (error) {
        console.error('Failed to delete customer:', error.message);
        res.status(500).json({ error: 'Server error: ' + error.message });
    }
};

export const getCustomer = async (req, res) => {
    try {
        const { id } = req.params;
        const customers = await loadCustomers();
        const customer = customers.find(c => c.id === id.toString());
        if (!customer) {
            return res.status(404).json({ error: 'Customer not found' });
        }
        res.status(200).json(customer);
    } catch (error) {
        console.error('Error getting customer:', error.message);
        res.status(500).json({ error: 'Failed to get customer' });
    }
};

export const showCustomersPage = async (req, res) => {
    try {
        console.log('🔄 Loading customers page - analyzing all order data...');

        // Load customers from order data using customer analytics service
        let customers = [];

        try {
            const customerAnalyticsService = (await import('../services/customerAnalyticsService.js')).default;
            console.log('📊 Running customer analytics on ALL orders (regardless of status)...');

            await customerAnalyticsService.analyzeCustomerData();
            const customerData = customerAnalyticsService.exportCustomerData();

            console.log(`✅ Customer analytics complete. Found ${customerData.length} unique customers`);

            // Transform customer analytics data to match the expected format
            customers = customerData.map((customer, index) => ({
                id: customer.customerId,
                name: customer.name,
                email: customer.email,
                address: customer.address,
                totalSpent: customer.totalSpent,
                orderCount: customer.orderCount,
                averageOrderValue: customer.averageOrderValue,
                lastOrderDate: customer.lastOrderDate,
                firstOrderDate: customer.firstOrderDate,
                orderStatuses: customer.orderStatuses,
                shippingAddresses: customer.shippingAddresses
            }));

            console.log('📋 Customer data summary:');
            customers.forEach(customer => {
                console.log(`  - ${customer.name} (${customer.email}): ${customer.orderCount} orders, $${customer.totalSpent.toFixed(2)} total`);
            });

        } catch (analyticsError) {
            console.warn('❌ Customer analytics service failed, falling back to local data:', analyticsError.message);
            console.warn('Stack trace:', analyticsError.stack);
            // Fallback to local customers file
            customers = await loadCustomers();
        }

        res.render('customers', {
            title: 'Customer Management',
            currentRoute: '/customers',
            pageStyles: ['/css/sidebar.css'],
            pageScripts: ['/js/customers.js', '/js/sidebar.js'],
            excludeParticles: true,
            excludeSidebar: false,
            excludeHeader: false,
            excludeFooter: false,
            customers,
            error: null
        });
    } catch (error) {
        console.error('❌ Error rendering customers page:', error.message);
        console.error('Stack trace:', error.stack);
        res.render('customers', {
            title: 'Customer Management', // Updated title
            currentRoute: '/customers',
            pageScripts: ['/js/customers.js'], // Corrected script name
            excludeParticles: true,
            customers: [],
            error: 'Failed to load customers'
        });
    }
};

export async function showCustomers(req, res) {
  try {
    const customers = await loadCustomers();
    res.render('customers', {
      title: 'Customers',
      currentRoute: '/customers',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      customers
    });
  } catch (error) {
    console.error('Error loading customers:', error);
    res.status(500).render('error', {
      pageTitle: 'Error',
      message: 'Failed to load customers page.',
      error,
      currentRoute: '/customers'
    });
  }
}

// Default export for compatibility
export default {
    addPromoCustomer,
    loadCustomers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomer,
    showCustomersPage
};
