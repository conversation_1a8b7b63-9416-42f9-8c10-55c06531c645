// Comprehensive Order Management Functionality Test
// This script tests all action buttons and interactive elements on the orders page

class OrderFunctionalityTester {
  constructor() {
    this.testResults = [];
    this.orderManager = null;
    this.testOrderId = null;
  }

  async runAllTests() {
    console.log('🧪 Starting comprehensive order management functionality tests...');
    
    // Wait for OrderManager to be initialized
    await this.waitForOrderManager();
    
    // Test all functionality
    await this.testOrderLoading();
    await this.testSearchAndFilter();
    await this.testRefreshButton();
    await this.testExportButton();
    await this.testDropdownActions();
    await this.testModalFunctionality();
    await this.testAPIEndpoints();
    
    // Display results
    this.displayTestResults();
  }

  async waitForOrderManager() {
    return new Promise((resolve) => {
      const checkForOrderManager = () => {
        if (window.orderManager || document.querySelector('#ordersTableBody')) {
          this.orderManager = window.orderManager;
          resolve();
        } else {
          setTimeout(checkForOrderManager, 100);
        }
      };
      checkForOrderManager();
    });
  }

  async testOrderLoading() {
    console.log('🔄 Testing order loading...');
    
    try {
      const response = await fetch('/api/orders?limit=5');
      const data = await response.json();
      
      if (response.ok && data.success && Array.isArray(data.data)) {
        this.addTestResult('Order Loading', 'PASS', `Loaded ${data.data.length} orders`);
        if (data.data.length > 0) {
          this.testOrderId = data.data[0].id;
        }
      } else {
        this.addTestResult('Order Loading', 'FAIL', 'Invalid API response format');
      }
    } catch (error) {
      this.addTestResult('Order Loading', 'FAIL', error.message);
    }
  }

  async testSearchAndFilter() {
    console.log('🔍 Testing search and filter functionality...');
    
    try {
      const searchInput = document.getElementById('searchInput');
      const statusFilter = document.getElementById('statusFilter');
      
      if (searchInput && statusFilter) {
        // Test search
        searchInput.value = 'test';
        searchInput.dispatchEvent(new Event('input'));
        
        // Test filter
        statusFilter.value = 'pending';
        statusFilter.dispatchEvent(new Event('change'));
        
        this.addTestResult('Search & Filter', 'PASS', 'Search and filter controls are functional');
      } else {
        this.addTestResult('Search & Filter', 'FAIL', 'Search or filter elements not found');
      }
    } catch (error) {
      this.addTestResult('Search & Filter', 'FAIL', error.message);
    }
  }

  async testRefreshButton() {
    console.log('🔄 Testing refresh button...');
    
    try {
      const refreshBtn = document.getElementById('refreshBtn');
      if (refreshBtn) {
        refreshBtn.click();
        this.addTestResult('Refresh Button', 'PASS', 'Refresh button is clickable');
      } else {
        this.addTestResult('Refresh Button', 'FAIL', 'Refresh button not found');
      }
    } catch (error) {
      this.addTestResult('Refresh Button', 'FAIL', error.message);
    }
  }

  async testExportButton() {
    console.log('📤 Testing export button...');
    
    try {
      const exportBtn = document.getElementById('exportBtn');
      if (exportBtn) {
        // Don't actually trigger download in test
        this.addTestResult('Export Button', 'PASS', 'Export button is present and clickable');
      } else {
        this.addTestResult('Export Button', 'FAIL', 'Export button not found');
      }
    } catch (error) {
      this.addTestResult('Export Button', 'FAIL', error.message);
    }
  }

  async testDropdownActions() {
    console.log('📋 Testing dropdown actions...');
    
    try {
      const dropdownButtons = document.querySelectorAll('[data-action]');
      const actions = ['update-status', 'shipping-label', 'generate-invoice', 'track-shipment', 'customer-profile'];
      
      let foundActions = [];
      dropdownButtons.forEach(btn => {
        const action = btn.dataset.action;
        if (actions.includes(action)) {
          foundActions.push(action);
        }
      });
      
      if (foundActions.length === actions.length) {
        this.addTestResult('Dropdown Actions', 'PASS', `All ${actions.length} actions found`);
      } else {
        this.addTestResult('Dropdown Actions', 'PARTIAL', `Found ${foundActions.length}/${actions.length} actions`);
      }
    } catch (error) {
      this.addTestResult('Dropdown Actions', 'FAIL', error.message);
    }
  }

  async testModalFunctionality() {
    console.log('🪟 Testing modal functionality...');
    
    try {
      const modals = [
        'statusUpdateModal',
        'shippingModal', 
        'trackingModal',
        'customerProfileModal'
      ];
      
      let foundModals = 0;
      modals.forEach(modalId => {
        if (document.getElementById(modalId)) {
          foundModals++;
        }
      });
      
      if (foundModals === modals.length) {
        this.addTestResult('Modal Elements', 'PASS', `All ${modals.length} modals found`);
      } else {
        this.addTestResult('Modal Elements', 'PARTIAL', `Found ${foundModals}/${modals.length} modals`);
      }
    } catch (error) {
      this.addTestResult('Modal Elements', 'FAIL', error.message);
    }
  }

  async testAPIEndpoints() {
    console.log('🌐 Testing API endpoints...');
    
    if (!this.testOrderId) {
      this.addTestResult('API Endpoints', 'SKIP', 'No test order ID available');
      return;
    }

    try {
      // Test individual order endpoint
      const orderResponse = await fetch(`/api/orders/${this.testOrderId}`);
      if (orderResponse.ok) {
        this.addTestResult('API - Get Order', 'PASS', 'Individual order endpoint working');
      } else {
        this.addTestResult('API - Get Order', 'FAIL', `Status: ${orderResponse.status}`);
      }

      // Test status update endpoint (without actually updating)
      const statusResponse = await fetch(`/api/orders/${this.testOrderId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'pending' }) // Keep same status
      });
      
      if (statusResponse.ok) {
        this.addTestResult('API - Status Update', 'PASS', 'Status update endpoint working');
      } else {
        this.addTestResult('API - Status Update', 'FAIL', `Status: ${statusResponse.status}`);
      }

    } catch (error) {
      this.addTestResult('API Endpoints', 'FAIL', error.message);
    }
  }

  addTestResult(testName, status, details) {
    this.testResults.push({
      test: testName,
      status: status,
      details: details,
      timestamp: new Date().toLocaleTimeString()
    });
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${testName}: ${status} - ${details}`);
  }

  displayTestResults() {
    console.log('\n🧪 TEST RESULTS SUMMARY:');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const partial = this.testResults.filter(r => r.status === 'PARTIAL').length;
    const skipped = this.testResults.filter(r => r.status === 'SKIP').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Partial: ${partial}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`📊 Total: ${this.testResults.length}`);
    
    // Show detailed results
    console.log('\nDetailed Results:');
    this.testResults.forEach(result => {
      const emoji = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : result.status === 'PARTIAL' ? '⚠️' : '⏭️';
      console.log(`${emoji} ${result.test}: ${result.details}`);
    });

    // Show notification
    if (window.notifications) {
      const successRate = Math.round((passed / this.testResults.length) * 100);
      window.notifications.showToast(
        `Order Management Test Complete: ${successRate}% success rate (${passed}/${this.testResults.length} passed)`,
        successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'error',
        'Functionality Test'
      );
    }
  }
}

// Auto-run tests when page loads (only if in test mode)
if (window.location.search.includes('test=true')) {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const tester = new OrderFunctionalityTester();
      tester.runAllTests();
    }, 2000); // Wait for page to fully load
  });
}

// Make tester available globally for manual testing
window.OrderFunctionalityTester = OrderFunctionalityTester;

// Add manual test trigger
console.log('🧪 Order Functionality Tester loaded. Run tests manually with:');
console.log('const tester = new OrderFunctionalityTester(); tester.runAllTests();');
