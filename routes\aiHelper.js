import express from 'express';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// AI Helper page route
router.get('/', (req, res) => {
  const aiHelperView = path.join(__dirname, '../views/ai-helper.ejs');
  if (fs.existsSync(aiHelperView)) {
    res.render('ai-helper', {
      title: 'AI Assistant | ShopEasly'
    });
  } else {
    res.render('aiHelper', {
      title: 'AI Assistant | ShopEasly'
    });
  }
});

// This file has been renamed to ai-helper.js for naming consistency. See routes/ai-helper.js.

export default router;
