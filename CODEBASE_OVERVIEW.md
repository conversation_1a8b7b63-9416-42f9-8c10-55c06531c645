# ShopEasly Admin – Codebase Overview

## Purpose

ShopEasly Admin is a Node.js/Express-based e-commerce admin panel prototype. It enables management of inventory, orders, customers, and business analytics. The project is modular, uses EJS for server-rendered views, and is designed for demonstration or internal use (not production).

---

## Core Technologies

- Node.js (ES6 modules)
- Express.js (web server, routing, middleware)
- EJS (templating for admin UI)
- express-session (session management)
- express-validator (input validation)
- Helmet (security headers)
- CORS (cross-origin resource sharing)
- Multer (file uploads)
- Flat JSON files or MockAPI (data storage)
- WebSocket (real-time features)

---

## Main Application Flow

- **Entry Point:** `app.js`
  - Loads environment variables, sets up Express, HTTP server, middleware, and session.
  - Configures security (Helmet, CORS, rate limiting).
  - Mounts routers for admin pages and APIs.
  - Handles authentication via session and login/logout routes.
  - Serves EJS views and static assets.
  - Starts the HTTP and WebSocket servers.

---

## Authentication & Authorization

- **File:** `utils/authHelper.js`
  - Provides password hashing, verification, and a session-based `ensureAuthenticated` middleware.
  - Middleware is used to protect admin panel pages and all non-GET API endpoints.
  - Public GET APIs (e.g., `/api/inventory`) are accessible for the shopping site frontend.

---

## Routing & Controllers

- **Routes:** Modularized in `/routes/` and `/routes/api/`
  - Each resource (orders, customers, inventory, etc.) has its own router.
  - API endpoints are RESTful and support CRUD operations.
  - Admin panel pages (dashboard, orders, customers, etc.) are rendered via EJS.

- **Controllers:** In `/controllers/`
  - Contain business logic for each resource.
  - Example: `dashboardController.js` aggregates data for the dashboard view.

---

## Services & Data Access

- **File:** `services/dataService.js`
  - Abstracts data access for products, materials, packaging, orders, and customers.
  - Supports both flat file storage (JSON) and MockAPI for development.
  - Implements caching for performance.
  - Provides CRUD methods for each resource.

- **Analytics:** `services/dashboardService.js`, `services/customerAnalyticsService.js`
  - Aggregate business metrics, generate AI suggestions, and compute analytics.

---

## Validation & Security

- **Validation:** All POST/PUT API endpoints use `express-validator` for robust input validation and sanitization.
- **Security:** 
  - Helmet sets secure HTTP headers and CSP.
  - CORS is restricted to trusted origins.
  - Rate limiting is applied to all `/api/` endpoints.
  - Session cookies are secure, HTTP-only, and have proper expiry.

---

## Data Model

- **Products/Inventory:** id, name, sku, stock, price, category, type, unit, status, supplier, imageUrl, description
- **Orders:** id, customerId, customerName, customerEmail, customerAddress, items (array), totalPrice, status, createdAt, trackingNumber, carrier
- **Customers:** id, name, email, address, phone, joinedAt, marketingPreferences, isPromoCustomer
- **Materials/Packaging:** id, name, stock, price, unit, type, status, notes, linkToBuy, supplier, dimensions, materialProperty

---

## Views & Static Assets

- **Views:** EJS templates in `/views/` for all admin UI pages.
- **Static Assets:** CSS, JS, and images in `/public/`.

---

## Extensibility

- **Modular Structure:** Clear separation of concerns (controllers, services, routes, utils).
- **Configurable Data Source:** Can switch between flat files and MockAPI; ready for DB migration.
- **Testing:** Structure supports unit/integration testing (test directory present).

---

## Limitations

- Not production-ready: Flat file storage is not scalable or secure.
- No role-based access: Single admin user only.
- No comprehensive automated tests.
- No real database: Migration to MongoDB/PostgreSQL is recommended for production.
- Notification/email/SMS stubs are present but not fully implemented.

---

## Security Model

- **Authentication:** Session-based, enforced via middleware.
- **Authorization:** Only authenticated users can access admin panel and write APIs.
- **Public APIs:** GET endpoints for inventory/products/orders are public for shopping site integration.
- **Input Validation:** All user input is validated and sanitized.
- **CORS:** Only trusted origins allowed.
- **Rate Limiting:** Prevents brute-force and abuse.

---

## Future Improvements

- Database migration
- Role-based access control
- Comprehensive automated testing
- Production-grade security (CSRF, XSS audit)
- API documentation (OpenAPI/Swagger)
- Modern frontend (React/Vue)

---

**Summary:**  
The codebase is a modular, session-authenticated, Express-based admin panel for e-commerce, with robust validation, basic security, and a clear separation of concerns. It is suitable for demonstration and internal use, but not for production without further enhancements.
