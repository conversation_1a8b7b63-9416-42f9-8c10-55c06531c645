#!/usr/bin/env node

/**
 * Deployment Verification Script for ShopEasly Admin
 * Checks if the application is properly deployed and accessible
 */

import fetch from 'node-fetch';

const PRODUCTION_URL = 'https://shop-easly-admin.onrender.com';

async function checkDeployment() {
  console.log('🚀 ShopEasly Admin Deployment Check');
  console.log('=' .repeat(50));
  console.log(`🔗 Testing URL: ${PRODUCTION_URL}`);
  console.log('');

  // Test 1: Basic connectivity
  console.log('1️⃣ Testing Basic Connectivity');
  console.log('-'.repeat(30));
  
  try {
    const response = await fetch(PRODUCTION_URL, {
      method: 'HEAD',
      timeout: 10000
    });
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    console.log(`   Headers:`);
    
    const importantHeaders = [
      'server',
      'x-powered-by',
      'content-type',
      'access-control-allow-origin',
      'access-control-allow-methods'
    ];
    
    importantHeaders.forEach(header => {
      const value = response.headers.get(header);
      if (value) {
        console.log(`     ${header}: ${value}`);
      }
    });
    
    if (response.status === 200) {
      console.log('   ✅ Server is responding');
    } else if (response.status === 404) {
      console.log('   ❌ Application not found (404)');
      console.log('   💡 This suggests the app is not deployed or not running');
    } else {
      console.log(`   ⚠️  Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`   ❌ Connection failed: ${error.message}`);
    console.log('   💡 This suggests the service is not accessible');
  }

  console.log('');

  // Test 2: Try to get the main page content
  console.log('2️⃣ Testing Main Page Content');
  console.log('-'.repeat(30));
  
  try {
    const response = await fetch(PRODUCTION_URL, {
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'ShopEasly-Deployment-Check/1.0'
      }
    });
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 200) {
      const content = await response.text();
      console.log(`   Content Length: ${content.length} characters`);
      
      // Check for expected content
      if (content.includes('ShopEasly') || content.includes('Shop Easly')) {
        console.log('   ✅ ShopEasly content detected');
      } else {
        console.log('   ⚠️  ShopEasly content not found in response');
      }
      
      if (content.includes('<html')) {
        console.log('   ✅ HTML content detected');
      } else {
        console.log('   ❌ No HTML content found');
      }
      
    } else {
      const errorContent = await response.text();
      console.log(`   Error Content: ${errorContent.substring(0, 200)}...`);
    }
    
  } catch (error) {
    console.log(`   ❌ Failed to fetch content: ${error.message}`);
  }

  console.log('');

  // Test 3: Check specific API endpoints
  console.log('3️⃣ Testing API Endpoints');
  console.log('-'.repeat(30));
  
  const endpoints = [
    '/api/production/health',
    '/api/inventory',
    '/dashboard'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${PRODUCTION_URL}${endpoint}`, {
        method: 'GET',
        timeout: 10000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'ShopEasly-Deployment-Check/1.0'
        }
      });
      
      console.log(`   ${endpoint}: ${response.status} ${response.statusText}`);
      
      if (response.status === 200) {
        console.log(`     ✅ Endpoint is working`);
      } else if (response.status === 404) {
        console.log(`     ❌ Endpoint not found`);
      } else if (response.status === 401) {
        console.log(`     🔒 Authentication required (expected for some endpoints)`);
      } else {
        console.log(`     ⚠️  Status: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`   ${endpoint}: ❌ ${error.message}`);
    }
  }

  console.log('');

  // Test 4: DNS and SSL check
  console.log('4️⃣ DNS and SSL Check');
  console.log('-'.repeat(30));
  
  try {
    const url = new URL(PRODUCTION_URL);
    console.log(`   Domain: ${url.hostname}`);
    console.log(`   Protocol: ${url.protocol}`);
    
    if (url.protocol === 'https:') {
      console.log('   ✅ HTTPS enabled');
    } else {
      console.log('   ⚠️  HTTP only (not secure)');
    }
    
  } catch (error) {
    console.log(`   ❌ URL parsing error: ${error.message}`);
  }

  console.log('');
  console.log('📋 Deployment Check Summary');
  console.log('='.repeat(50));
  console.log('If you see 404 errors:');
  console.log('  • Check if the app is deployed on Render');
  console.log('  • Verify the build completed successfully');
  console.log('  • Check Render logs for startup errors');
  console.log('  • Ensure the start command is correct');
  console.log('');
  console.log('If you see connection errors:');
  console.log('  • Check if the Render service is running');
  console.log('  • Verify the domain name is correct');
  console.log('  • Check Render service status');
  console.log('');
  console.log('Next steps:');
  console.log('  1. Check Render dashboard for service status');
  console.log('  2. Review deployment logs');
  console.log('  3. Verify environment variables are set');
  console.log('  4. Test locally with: npm start');
}

// Run the check
checkDeployment().catch(console.error);
