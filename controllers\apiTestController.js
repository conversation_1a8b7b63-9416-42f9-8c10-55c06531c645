// controllers/apiTestController.js
export async function showApiTest(req, res) {
  try {
    // You may want to load some data for the API test page
    res.render('api-test', {
      title: 'API Test',
      currentRoute: '/api-test',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      orders: []
    });
  } catch (error) {
    res.render('api-test', {
      title: 'API Test',
      currentRoute: '/api-test',
      pageStyles: ['/css/sidebar.css'],
      pageScripts: ['/js/sidebar.js'],
      excludeParticles: true,
      excludeSidebar: false,
      orders: [],
      error: error.message
    });
  }
}
