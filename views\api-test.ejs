<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MockAPI Test - <%= title %></title>
    <link rel="stylesheet" href="/css/main.css">
    <style>
        .api-test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            color: #333;
            margin-bottom: 1rem;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        .refresh-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 1rem;
        }
        .refresh-btn:hover {
            background-color: #0056b3;
        }
        .json-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <main class="content-area">
        <div class="api-test-container">
            <h1>MockAPI Integration Test</h1>
            <p>This page tests the integration with your MockAPI at: <code>https://681a3a3c1ac1155635084e35.mockapi.io</code></p>

            <!-- Orders Test Section -->
            <div class="test-section">
                <h2>Orders API Test</h2>
                <div class="status-info">
                    <span class="status-badge <%= orders && orders.length > 0 ? 'status-success' : 'status-error' %>">
                        <%= orders && orders.length > 0 ? 'Connected' : 'No Data' %>
                    </span>
                    <span style="margin-left: 1rem;">
                        <%= orders ? orders.length : 0 %> orders loaded
                    </span>
                </div>

                <button class="refresh-btn" onclick="window.location.reload()">Refresh Data</button>

                <% if (orders && orders.length > 0) { %>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Total Price</th>
                                <th>Status</th>
                                <th>Created At</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% orders.slice(0, 10).forEach(order => { %>
                                <tr>
                                    <td>#<%= order.id %></td>
                                    <td><%= order.customerName || 'N/A' %></td>
                                    <td><%= order.customerEmail || 'N/A' %></td>
                                    <td>$<%= parseFloat(order.totalPrice || 0).toFixed(2) %></td>
                                    <td>
                                        <span class="status-badge status-<%= order.status === 'completed' ? 'success' : 'error' %>">
                                            <%= order.status || 'unknown' %>
                                        </span>
                                    </td>
                                    <td><%= new Date(order.createdAt).toLocaleDateString() %></td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>

                    <% if (orders.length > 10) { %>
                        <p style="margin-top: 1rem; color: #666;">
                            Showing first 10 of <%= orders.length %> orders. 
                            <a href="/orders">View all orders</a>
                        </p>
                    <% } %>

                    <!-- Raw Data Preview -->
                    <h3 style="margin-top: 2rem;">Raw Data Sample (First Order)</h3>
                    <div class="json-preview">
<%= JSON.stringify(orders[0], null, 2) %>
                    </div>
                <% } else { %>
                    <p style="color: #721c24; margin-top: 1rem;">
                        No orders data available. Check the server logs for connection issues.
                    </p>
                <% } %>
            </div>

            <!-- API Endpoints Test -->
            <div class="test-section">
                <h2>Available Endpoints</h2>
                <ul>
                    <li>
                        <strong>Orders:</strong> 
                        <a href="https://681a3a3c1ac1155635084e35.mockapi.io/orders" target="_blank">
                            /orders
                        </a>
                        <span class="status-badge status-success">Available</span>
                    </li>
                    <li>
                        <strong>Inventory:</strong> 
                        <a href="https://681a3a3c1ac1155635084e35.mockapi.io/inventory" target="_blank">
                            /inventory
                        </a>
                        <span class="status-badge status-error">Not Found</span>
                    </li>
                    <li>
                        <strong>Customers:</strong> 
                        <a href="https://681a3a3c1ac1155635084e35.mockapi.io/customers" target="_blank">
                            /customers
                        </a>
                        <span class="status-badge status-error">Not Found</span>
                    </li>
                </ul>
            </div>

            <!-- Configuration Info -->
            <div class="test-section">
                <h2>Configuration</h2>
                <ul>
                    <li><strong>MockAPI URL:</strong> https://681a3a3c1ac1155635084e35.mockapi.io</li>
                    <li><strong>Cache TTL:</strong> 5 minutes</li>
                    <li><strong>Fallback:</strong> Local JSON files</li>
                    <li><strong>Data Source:</strong> <%= process.env.USE_MOCK_API !== 'false' ? 'MockAPI (Primary)' : 'Local Files' %></li>
                </ul>
            </div>

            <div style="margin-top: 2rem; text-align: center;">
                <a href="/dashboard" style="color: #007bff; text-decoration: none;">← Back to Dashboard</a>
            </div>
        </div>
    </main>
</body>
</html>
