// File: ordersController.js
import path from 'path';
import fs from 'fs/promises';
import WebSocket from 'ws';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const ordersPath = path.join(__dirname, '../data/orders.json');
const inventoryPath = path.join(__dirname, '../data/inventory.json');

async function load(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    console.error(`Failed to read ${filePath}:`, err);
    return [];
  }
}

async function save(filePath, data) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
  } catch (err) {
    console.error(`Failed to write ${filePath}:`, err);
  }
}

async function placeOrder(req, res, wss) {
  try {
    const { customerId, customerName, customerEmail, customerAddress, items, totalPrice } = req.body;

    if (!customerId || !customerName || !customerEmail || !customerAddress || !Array.isArray(items) || items.length === 0 || !totalPrice) {
      return res.status(400).json({ error: 'Invalid order data' });
    }

    const inventory = await load(inventoryPath);

    for (const orderItem of items) {
      const inventoryItem = inventory.find(i => i.id === orderItem.id);
      if (!inventoryItem) {
        return res.status(404).json({ error: `Item with ID ${orderItem.id} not found in inventory` });
      }
      if (inventoryItem.status !== 'In Stock' && inventoryItem.status !== 'Low Stock') {
        return res.status(400).json({ error: `Item "${inventoryItem.name}" is marked "${inventoryItem.status}" — cannot proceed with order.` });
      }
      if (orderItem.quantity > inventoryItem.stock) {
        return res.status(400).json({ error: `Not enough stock for "${inventoryItem.name}". Requested: ${orderItem.quantity}, Available: ${inventoryItem.stock}` });
      }
    }

    const newOrder = {
      id: uuidv4(),
      customer: {
        id: customerId,
        name: customerName,
        email: customerEmail,
        address: customerAddress
      },
      items: items.map(item => ({ ...item, lineTotal: item.quantity * item.price })),
      totalPrice,
      status: 'Order Received',
      createdAt: new Date().toISOString()
    };

    const orders = await load(ordersPath);
    orders.push(newOrder);
    await save(ordersPath, orders);

    for (const orderItem of items) {
      const inventoryItemIndex = inventory.findIndex(i => i.id === orderItem.id);
      if (inventoryItemIndex !== -1) {
        inventory[inventoryItemIndex].stock -= orderItem.quantity;
        if (inventory[inventoryItemIndex].stock <= 0) {
          inventory[inventoryItemIndex].status = 'Out of Stock';
        } else if (inventory[inventoryItemIndex].stock <= 5) {
          inventory[inventoryItemIndex].status = 'Low Stock';
        } else {
          inventory[inventoryItemIndex].status = 'In Stock';
        }
      }
    }
    await save(inventoryPath, inventory);

    // Removed MockAPI sync for local-only persistence
    // If you want to sync with a remote API, add it here as an optional feature

    if (wss) {
      wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({ type: 'newOrder', order: newOrder }));
        }
      });
    }

    res.json({ success: true, order: newOrder });
  } catch (error) {
    console.error('Failed to process order:', error.message);
    res.status(500).json({ error: 'Server error: ' + error.message });
  }
}

async function getAllOrders(req, res, next) {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const orders = await load(ordersPath);

    const startIndex = (page - 1) * limit;
    const paginatedOrders = orders.slice(startIndex, startIndex + limit);

    res.render('orders', {
      title: 'Orders',
      currentRoute: '/orders',
      orders: paginatedOrders,
      currentPage: page,
      totalPages: Math.ceil(orders.length / limit)
    });
  } catch (error) {
    next(error);
  }
}

async function getOrderById(req, res, next) {
  try {
    const orderId = req.params.id;
    const orders = await load(ordersPath);
    const order = orders.find(o => o.id === orderId);

    if (!order) {
      return res.status(404).send('Order not found');
    }

    res.render('orderDetail', { title: `Order ${order.id}`, order });
  } catch (error) {
    next(error);
  }
}

async function updateOrder(req, res) {
  try {
    const orderId = req.params.id;
    const { status, items } = req.body;
    const orders = await load(ordersPath);
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex === -1) {
      return res.status(404).json({ error: 'Order not found' });
    }

    if (status) orders[orderIndex].status = status;
    if (items) orders[orderIndex].items = items;

    await save(ordersPath, orders);
    res.json({ success: true, order: orders[orderIndex] });
  } catch (error) {
    console.error('Error updating order:', error.message);
    res.status(500).json({ error: 'Failed to update order' });
  }
}

async function deleteOrder(req, res) {
  try {
    const orderId = req.params.id;
    const orders = await load(ordersPath);
    const orderIndex = orders.findIndex(o => o.id === orderId);

    if (orderIndex === -1) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const [deletedOrder] = orders.splice(orderIndex, 1);
    await save(ordersPath, orders);

    res.json({ success: true, order: deletedOrder });
  } catch (error) {
    console.error('Error deleting order:', error.message);
    res.status(500).json({ error: 'Failed to delete order' });
  }
}

export const showOrders = async (req, res) => {
  try {
    const orders = await load(ordersPath);
    res.render('orders', {
      title: 'Orders',
      currentRoute: '/orders',
      pageStyles: ['/css/sidebar.css', '/css/orders.css'],
      pageScripts: ['/js/notifications.js', '/js/sidebar.js', '/js/orders-production.js'],
      excludeParticles: true,
      excludeSidebar: false,
      orders
    });
  } catch (error) {
    console.error('Error loading orders:', error);
    res.status(500).render('error', {
      pageTitle: 'Error',
      message: 'Failed to load orders page.',
      error,
      currentRoute: '/orders'
    });
  }
};

export {
  getAllOrders,
  getOrderById,
  placeOrder,
  updateOrder,
  deleteOrder
};
