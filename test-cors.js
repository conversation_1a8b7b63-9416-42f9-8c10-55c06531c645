#!/usr/bin/env node

/**
 * CORS Test Script for ShopEasly Admin
 * Tests CORS configuration and API endpoints
 */

import fetch from 'node-fetch';

const PRODUCTION_URL = 'https://shop-easly-admin.onrender.com';
const TEST_ORIGINS = [
  'https://shop-easly-admin.onrender.com',
  'https://shopeasly.onrender.com',
  'http://localhost:3000',
  null // No origin (server-to-server)
];

const API_ENDPOINTS = [
  '/api/inventory',
  '/api/orders',
  '/api/customers',
  '/api/events',
  '/api/production/health'
];

async function testCORS() {
  console.log('🧪 Testing CORS Configuration for ShopEasly Admin');
  console.log('=' .repeat(60));

  for (const origin of TEST_ORIGINS) {
    console.log(`\n🔍 Testing with origin: ${origin || 'No Origin'}`);
    console.log('-'.repeat(40));

    for (const endpoint of API_ENDPOINTS) {
      try {
        const headers = {
          'Content-Type': 'application/json'
        };

        if (origin) {
          headers['Origin'] = origin;
        }

        const response = await fetch(`${PRODUCTION_URL}${endpoint}`, {
          method: 'GET',
          headers
        });

        const corsHeaders = {
          'Access-Control-Allow-Origin': response.headers.get('access-control-allow-origin'),
          'Access-Control-Allow-Credentials': response.headers.get('access-control-allow-credentials'),
          'Access-Control-Allow-Methods': response.headers.get('access-control-allow-methods')
        };

        console.log(`  ${endpoint}: ${response.status} ${response.statusText}`);
        
        if (response.status === 200) {
          console.log(`    ✅ Success`);
          if (corsHeaders['Access-Control-Allow-Origin']) {
            console.log(`    🔗 CORS Origin: ${corsHeaders['Access-Control-Allow-Origin']}`);
          }
        } else {
          console.log(`    ❌ Failed`);
          const errorText = await response.text();
          console.log(`    📝 Error: ${errorText.substring(0, 100)}...`);
        }

      } catch (error) {
        console.log(`  ${endpoint}: ❌ Network Error`);
        console.log(`    📝 Error: ${error.message}`);
      }
    }
  }

  // Test the main page
  console.log(`\n🏠 Testing Main Page`);
  console.log('-'.repeat(40));
  
  try {
    const response = await fetch(PRODUCTION_URL);
    console.log(`  /: ${response.status} ${response.statusText}`);
    
    if (response.status === 200) {
      console.log(`    ✅ Main page loads successfully`);
    } else {
      console.log(`    ❌ Main page failed to load`);
    }
  } catch (error) {
    console.log(`  /: ❌ Network Error - ${error.message}`);
  }

  // Test WebSocket connection
  console.log(`\n🔌 Testing WebSocket Connection`);
  console.log('-'.repeat(40));

  try {
    // Note: This is a basic test - WebSocket testing from Node.js requires additional setup
    console.log('  WebSocket URL: wss://shop-easly-admin.onrender.com');
    console.log('  ℹ️  WebSocket testing requires browser environment or ws library');
    console.log('  ✅ WebSocket endpoint should be available');
  } catch (error) {
    console.log(`  ❌ WebSocket test error: ${error.message}`);
  }

  console.log('\n📊 Test Summary');
  console.log('='.repeat(60));
  console.log('✅ CORS configuration tested');
  console.log('✅ API endpoints tested');
  console.log('✅ Main page tested');
  console.log('ℹ️  Check Render logs for detailed CORS debugging info');
  console.log('\n🎯 CORS Test Complete');
}

// Run the test
testCORS().catch(console.error);
