// File: routes/orderManagementRoutes.js
// Main route for Order Management (formerly ordersRoutes.js)

import express from 'express';
import { showOrders } from '../controllers/ordersController.js';

const router = express.Router();

// Canonical Orders page route
router.get('/', showOrders);

// Sample order data (replace with database or service call in production)
const orders = [
  { id: 1, productId: 'P001', quantity: 3, createdAt: '2023-05-15T10:00:00Z', status: 'Delivered' },
  { id: 2, productId: 'P002', quantity: 2, createdAt: '2023-05-16T14:30:00Z', status: 'Processing' },
  { id: 3, productId: 'P003', quantity: 5, createdAt: '2023-05-17T09:15:00Z', status: 'Shipped' },
  { id: 4, productId: 'P004', quantity: 1, createdAt: '2023-05-18T16:45:00Z', status: 'Pending' }
];

// Render Order Management page
router.get('/', (req, res) => {
  res.render('orders', {
    orders,
    totalOrders: orders.length,
    error: null
  });
});

// Provide order data as JSON
router.get('/api/data', (req, res) => {
  res.json(orders);
});

export default router;
