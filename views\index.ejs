<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shop Easly - Modern E-commerce Management</title>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Link to external CSS -->
  <link rel="stylesheet" href="/css/index.css">
</head>

<body>
  <main class="content-area">
    <!-- Background Elements -->
    <div class="bg-gradient"></div>
    <div class="bg-grid"></div>
    <div class="bg-logo">
      <img src="/images/logo.png" alt="ShopEasly Logo" class="logo-background">
    </div>
    <div id="particles-js"></div>

    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-brand">
        <img src="/images/logo.png" alt="Shop Easly Logo" class="nav-logo">
        <span>ShopEasly</span>
      </div>
    </nav>

    <!-- Hero Section -->
    <main class="hero-section">
      <div class="hero-content">
        <div class="hero-badge">
          <i class="fas fa-rocket"></i>
          <span>Welcome to Your ShopEasly Command Center</span>
        </div>

        <h1 class="hero-title">
          <span class="title-line">Command Center for</span>
          <span class="title-highlight">ShopEasly</span>
        </h1>

        <p class="hero-description">
          Track inventory, manage orders, and boost sales with smart insights.
          <strong>Thrive now.</strong>
        </p>

        <div class="hero-actions">
          <button id="enterButton" class="btn-primary" onclick="handleEnterDashboard()">
            <span>Enter Dashboard</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>

        <div class="hero-stats">
          <div class="stat-item">
            <div class="stat-number"><%= productsManaged %></div>
            <div class="stat-label">Products Managed</div>
          </div>
          <div class="stat-item">
            <div class="stat-number"><%= totalOrders %></div>
            <div class="stat-label">Orders Daily</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">Uptime</div>
          </div>
        </div>
      </div>

      <div class="hero-visual">
        <div class="dashboard-preview">
          <div class="preview-header">
            <div class="preview-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="preview-title">Shop Easly Dashboard</div>
          </div>
          <div class="preview-content">
            <div class="preview-sidebar">
              <div class="sidebar-item active">
                <i class="fas fa-chart-line"></i>
                <span>Dashboard</span>
              </div>
              <div class="sidebar-item">
                <i class="fas fa-box"></i>
                <span>Inventory</span>
              </div>
              <div class="sidebar-item">
                <i class="fas fa-shopping-cart"></i>
                <span>Orders</span>
              </div>
              <div class="sidebar-item">
                <i class="fas fa-users"></i>
                <span>Customers</span>
              </div>
            </div>
            <div class="preview-main">
              <div class="preview-cards">
                <div class="preview-card">
                  <div class="card-icon revenue">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                  <div class="card-content">
                    <div class="card-value">$<%= totalRevenue.toLocaleString() %></div>
                    <div class="card-label">Revenue</div>
                  </div>
                </div>
                <div class="preview-card">
                  <div class="card-icon orders">
                    <i class="fas fa-shopping-bag"></i>
                  </div>
                  <div class="card-content">
                    <div class="card-value"><%= totalOrders.toLocaleString() %></div>
                    <div class="card-label">Orders</div>
                  </div>
                </div>
              </div>
              <div class="preview-chart">
                <div class="chart-bars">
                  <div class="bar" style="height: 60%"></div>
                  <div class="bar" style="height: 80%"></div>
                  <div class="bar" style="height: 45%"></div>
                  <div class="bar" style="height: 90%"></div>
                  <div class="bar" style="height: 70%"></div>
                  <div class="bar" style="height: 95%"></div>
                  <div class="bar" style="height: 85%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="floating-element" style="top: 10%; left: 10%;">
          <i class="fas fa-chart-pie"></i>
        </div>
        <div class="floating-element" style="top: 20%; right: 15%;">
          <i class="fas fa-bolt"></i>
        </div>
        <div class="floating-element" style="bottom: 30%; left: 5%;">
          <i class="fas fa-shield-alt"></i>
        </div>
      </div>
    </main>
  </main>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
  <script src="/js/index.js"></script>
</body>
</html>
