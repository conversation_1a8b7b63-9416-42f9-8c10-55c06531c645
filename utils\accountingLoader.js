import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const accountingPath = path.join(__dirname, '../data/accounting.json');

// Load accounting data
async function loadAccounting() {
    try {
        const data = await fs.readFile(accountingPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading accounting data:', error.message);
        return [];
    }
}

// Save accounting data
async function saveAccounting(accounting) {
    try {
        await fs.writeFile(accountingPath, JSON.stringify(accounting, null, 2));
    } catch (error) {
        console.error('Error saving accounting data:', error.message);
        throw error;
    }
}

export { loadAccounting, saveAccounting };